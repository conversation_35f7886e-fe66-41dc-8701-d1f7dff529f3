import React, { type ReactNode, useRef } from 'react';
import useObjectProfileActions from 'shared/components/layouts/ProfileLayout/ProfileLayout.Content/useObjectProfileActions';
import Object3DotActions from 'shared/components/layouts/ProfileLayout/ProfileLayout.Header/partials/ProfileHeader.actions';
import HorizontalInfoBox from 'shared/components/molecules/HorizontalInfoBox';
import urls from 'shared/constants/urls';
import Avatar from 'shared/uikit/Avatar';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import Image from 'shared/uikit/Image';
import ObjectLink from 'shared/uikit/Link/ObjectLink';
import Media from 'shared/uikit/Media';
import PopperMenu from 'shared/uikit/PopperMenu';
import cnj from 'shared/uikit/utils/cnj';
import classes from './SearchObjectInfo.component.module.scss';
import SearchObjectInfoTitles from './SearchObjectInfo.Titles';
import type { NetworkModelType } from '@shared/types/page';

export type Action = { icon: string; label: string; onClick?: Function };

interface SearchObjectInfoProps {
  data: {
    object: any;
    id: string;
    username: string;
    firstText?: string;
    secondText?: string;
    thirdText?: string;
    fourthText?: string;
    image?: any;
    network?: NetworkModelType;
    back?: boolean;
    isPage?: boolean;
    isBlocked?: boolean;
    bg?: string;
  };
  className?: string;
  followerData?: Array<{ onClick: () => void; title: string; value: string }>;
  preventShareVia?: boolean;
  hide3Dot?: boolean;
  customActions?: ReactNode;
}

const SearchObjectInfo = ({
  data,
  className,
  followerData = [],
  preventShareVia,
  hide3Dot,
  customActions,
}: SearchObjectInfoProps) => {
  const { image } = data;
  const contianerRef = useRef(null);
  const placeholder = data.isPage ? urls.placeholderPage : urls.placeholderUser;

  const profileLayoutActions =
    customActions ??
    useObjectProfileActions({
      objectId: data?.id,
    });

  const visibleActions = !customActions && !Array.isArray(profileLayoutActions);

  return (
    <Flex className={cnj(classes.searchObjectInfoRoot, className)}>
      <Image
        alt=" "
        resolution={data.bg ? 'medium' : 'original'}
        className={classes.bgImage}
        src={data.bg || placeholder}
      />
      <Flex className={classes.searchObjectInfoContainer} ref={contianerRef}>
        <Flex className={classes.wrapper}>
          <Media greaterThan="tablet">
            <Flex>
              <Flex flexDir="row" className={classes.leftWrapper}>
                <ObjectLink username={data.username} objectId={data.id}>
                  <Avatar
                    className={classes.avatar}
                    imgSrc={image}
                    isCompany={data?.isPage}
                    name={data?.firstText || data?.username}
                    size="xl"
                    bordered
                  />
                </ObjectLink>
                <Flex className={classes.topWrapper}>
                  <Flex flexDir="row" className={classes.firstRow}>
                    <SearchObjectInfoTitles
                      className={classes.jobCompanyTitles}
                      data={data}
                    />
                  </Flex>
                  <Flex flexDir="row" className={classes.followersWrapper}>
                    <HorizontalInfoBox
                      data={followerData}
                      className={classes.fullWidth}
                      direction="row"
                    />
                  </Flex>
                </Flex>
                {!hide3Dot && !data?.isBlocked && (
                  <Flex className={classes.editIcon}>
                    <PopperMenu
                      placement="bottom-end"
                      closeOnScroll
                      buttonComponent={
                        <IconButton
                          type="fas"
                          name="ellipsis-h"
                          size="md"
                          className={classes.withMargin}
                        />
                      }
                    >
                      <Object3DotActions
                        username={data.username}
                        objectId={data.id}
                        dontShare={preventShareVia}
                      />
                    </PopperMenu>
                  </Flex>
                )}
              </Flex>
            </Flex>
          </Media>
          <Media lessThan="midDesktop">
            <Flex>
              <Flex flexDir="row" className={classes.leftWrapper}>
                <ObjectLink username={data.username} objectId={data.id}>
                  <Avatar
                    imgSrc={image}
                    isCompany
                    name={data?.username}
                    size="slg"
                  />
                </ObjectLink>
                <Flex className={classes.topWrapper}>
                  <SearchObjectInfoTitles
                    className={classes.jobCompanyTitles}
                    data={data}
                  />
                </Flex>
              </Flex>
              <Flex flexDir="row" className={classes.followersWrapper}>
                <HorizontalInfoBox data={followerData} />
              </Flex>
            </Flex>
          </Media>
          {customActions ? (
            <>{customActions}</>
          ) : visibleActions ? (
            <Flex className={classes.followBtn}>{profileLayoutActions}</Flex>
          ) : null}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default SearchObjectInfo;
