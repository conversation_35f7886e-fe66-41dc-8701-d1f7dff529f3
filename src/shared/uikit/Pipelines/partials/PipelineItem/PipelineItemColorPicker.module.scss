@use "sass:math";

@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .dot {
    padding: variables(xLargeGutter) * 0.25;
    background-color: colors(darkSecondary_hover);
    border-radius: variables(gutter) * 0.25;
    &.disabled {
      opacity: 0.3;
    }
  }
  .item {
    padding: math.div(variables(xLargeGutter), 3);
    border-radius: variables(gutter) * 0.25;
    &:hover {
      background-color: colors(darkSecondary_hover);
    }
    &.active {
      background-color: colors(brand);
    }

    .itemIcon {
      border: 1px solid white;
      border-radius: 50%;
    }
  }
  .popper {
    z-index: 4;
    background-color: colors(background);
    border: 1px solid colors(techGray_20);
    padding: variables(gutter) * 0.5;
    border-radius: 12px;
    margin-top: variables(gutter) * 0.25;
  }
  .colorsWrap {
    gap: variables(gutter) * 0.25;
  }
}
