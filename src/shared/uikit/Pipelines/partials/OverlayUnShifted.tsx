import Flex from '@shared/uikit/Flex';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';

export const OverlayUnShifted = ({ isDragging }: { isDragging: boolean }) => {
  const { t } = useTranslation();

  return (
    <div
      className={cnj(
        'h-[104px]  !border !border-solid !border-error rounded z-2 relative overflow-hidden',
        isDragging ? 'rotate-[3deg]' : ''
      )}
    >
      <Flex className="backdrop-blur-md text-error z-[-1] absolute top-0 left-0 bg-[color:rgb(var(--popOverBg_white)/0.5)] flex items-center justify-center text-sm h-full w-full">
        {t('default_stages_cant_be_moved')}
      </Flex>
    </div>
  );
};
