@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .itemRoot {
    padding: variables(xLarge<PERSON>utter) * 0.5;
    border: 1px solid colors(techGray_20);
    border-radius: variables(gutter) * 0.25;
    gap: variables(xLargeGutter) * 0.5;
    user-select: none;
    cursor: pointer;
    margin-bottom: variables(xLargeGutter) * 0.5;
    position: relative;
    // background-color: colors(gray_5);
    &.disabled {
      cursor: initial;
    }

    .icon {
      padding: variables(gutter) * 0.25;
    }
    .dot {
      padding: variables(xLargeGutter) * 0.25;
      background-color: colors(darkSecondary_hover);
      border-radius: variables(gutter) * 0.25;
    }
    .appTrackWrapper {
      margin-left: auto;
      flex-direction: row;
      align-items: center;
      gap: variables(gutter) * 0.5;

      .appTrack {
        flex-direction: row;
        align-items: center;
        gap: variables(gutter) * 0.25;
      }
    }
    .switch {
      flex: unset;
      padding: 0;
      margin-right: variables(gutter) * 0.25;
    }
    .elDisabled {
      opacity: 0.3;
    }
  }
  .addButton {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: variables(gutter) + 1;
    border: 1px dashed colors(techGray_20);
    border-radius: variables(gutter) * 0.25;
    flex-direction: row;
    gap: variables(gutter) * 0.5;
  }
  .addInputRoot {
    flex-direction: row;
    gap: variables(xLargeGutter) * 0.5;
    padding-right: variables(xLargeGutter) * 0.5;

    .inputRoot {
      flex: 1;
      .inputWrap {
        height: 58px;
      }
    }
  }
  .inputRootInItem {
    margin-top: unset;
    margin-bottom: variables(xLargeGutter) * 0.5;
  }
}
