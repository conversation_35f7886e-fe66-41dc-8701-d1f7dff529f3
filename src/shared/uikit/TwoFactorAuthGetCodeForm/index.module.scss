@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .formButtonWrap {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: variables(gutter);
  }
  .alertMessage {
    margin-top: 24px;
  }
  .cancelBtn {
    margin-right: variables(gutter) * 0.5;
  }
  .backupBtn {
    padding-left: 0;
  }
  .helperText {
    margin: variables(gutter) * 0.5 0;
  }
  .fullWidth {
    width: 100%;
  }
  .thinUseBkpCode {
    padding: 0;
    margin: variables(gutter) * 0.5 0 variables(gutter) 0;
  }
  @media (min-width: breakpoints(tablet)) {
    .formButtonWrap {
      margin-top: variables(largeGutter);
    }
    .cancelBtn {
      margin-right: variables(largeGutter) * 0.5;
    }
    .helperText {
      margin: variables(xLargeGutter) * 0.5 0;
    }
  }
}
