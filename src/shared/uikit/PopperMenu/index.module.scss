@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .popperContainer {
    z-index: zIndex(popperMenu);
  }
  .popperWrapper {
    background-color: colors(background);
    border: 1px solid colors(techGray_20);
    padding: variables(gutter) * 0.5;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    cursor: default;
  }
  .arrow {
    position: absolute;
    top: -9px;
    transform: translateX(-12px);
    &:before {
      content: '';
      position: absolute;
      top: -2px;
      width: 0;
      height: 0;
      border-left: 12px solid colors(transparent);
      border-right: 12px solid colors(transparent);
      border-bottom: 12px solid colors(techGray_20);
    }
    &:after {
      content: '';
      position: absolute;
      left: 2px;
      top: 0;
      width: 0;
      height: 0;
      border-left: 10px solid colors(transparent);
      border-right: 10px solid colors(transparent);
      border-bottom: 10px solid colors(background);
    }
  }
}
