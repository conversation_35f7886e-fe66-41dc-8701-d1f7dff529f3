@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .notFoundRoot {
    align-items: center;
    justify-content: center;
    padding: variables(gutter) variables(gutter) variables(gutter) * 2
      variables(gutter);
    background: colors(background2);
    height: 100%;
  }
  .notFoundImgWrap {
    padding-top: variables(gutter);
    width: 220px;
    height: auto;
    align-self: center;
  }
  .title {
    margin-top: 80px;
  }
  .message {
    margin-top: variables(gutter);
  }
  .iconWrap {
    flex-direction: row;
    align-items: center;
    margin-top: variables(gutter);
  }
  .back {
    margin-right: 4px;
    flex-direction: row;
  }

  @media (min-width: breakpoints(tablet)) {
    .notFoundRoot {
      padding: variables(largeGutter) variables(largeGutter) * 3
        variables(largeGutter);
    }
    .notFoundImgWrap {
      padding-top: variables(largeGutter);
    }
    .message {
      margin-top: variables(largeGutter);
    }
    .iconWrap {
      margin-top: variables(largeGutter);
    }
  }
}
