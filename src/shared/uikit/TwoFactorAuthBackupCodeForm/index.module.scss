@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .formButtonWrap {
    flex-direction: row;
    align-items: center;
    margin-top: variables(gutter);
  }
  .alertMessage {
    margin-top: 24px;
  }
  .cancelBtn {
    margin-right: variables(gutter) * 0.5;
  }
  .forgetBtn {
    padding-left: 0;
  }
  .helperText {
    margin: variables(gutter) * 0.5 0;
  }
  .button {
    width: 100%;
  }

  @media (min-width: breakpoints(tablet)) {
    .formButtonWrap {
      margin-top: variables(largeGutter);
      justify-content: flex-end;
    }
    .cancelBtn {
      margin-right: variables(largeGutter) * 0.5;
    }
    .helperText {
      margin: variables(xLargeGutter) * 0.5 0;
    }
  }
}
