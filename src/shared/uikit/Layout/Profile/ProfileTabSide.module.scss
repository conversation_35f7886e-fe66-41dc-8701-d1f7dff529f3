@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .profileTabSideRoot {
    display: flex;
    width: 100%;
  }
  .onlyDesktop {
    display: none;
  }

  @media (min-width: breakpoints(tablet)) {
    .profileTabSideRoot {
      display: flex;
      max-width: variables(rightPaneMaxWidth);
    }
    .left {
      margin-left: variables(largeGutter);
    }
    .right {
      margin-right: variables(largeGutter);
    }
  }
}
