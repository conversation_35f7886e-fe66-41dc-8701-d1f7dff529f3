@import '/src/shared/theme/theme.scss';

// const MAIN_CENTER_WRAPPER_ID = 'mainCenterWrapper';
@layer uikit2 {
  .mobileHeader {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 5;
    background: colors(background);
    padding: variables(gutter);
  }

  .titleWrapper {
    flex-direction: row;
    align-items: center;
  }

  .searchInput {
    margin-top: variables(gutter) * 0.5;
  }

  .paperRoot {
    padding: 0;
    margin-bottom: variables(gutter) * 0.5;
    overflow: hidden;
  }

  .tabPageHeaderContainer {
    width: 100%;
    height: auto;
    margin: auto;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    padding: 0;
  }

  .headerLeft {
    flex: 1;
    height: 100%;
  }

  .title {
    display: none;
  }

  .titleWithTab {
    margin: 53px 0;
  }

  .desktopTabsContainer {
    display: none;
  }

  .mobileTabsContainer {
    width: 100%;
  }

  .mobileLinksRoot {
    background-color: colors(background2);
  }

  .desktopLinksRoot {
    & > * {
      padding: 0;
    }
  }

  .headerRight {
    padding-top: variables(largeGutter);
    width: 100%;
    align-items: center;
  }

  .backActionButton {
    margin-right: variables(gutter);
  }

  @media (min-width: 350px) {
    .mobileLinksRoot {
      & > *:first-child > div {
        justify-content: center;
      }
    }
  }

  @media (min-width: breakpoints(tablet)) {
    .mobileHeader {
      display: none;
    }
    .paperRoot {
      border-radius: 12px;
    }
    .title {
      display: flex;
      margin: auto 0;
    }

    .titleWithTab {
      margin: 53px 0;
    }

    .tabPageHeaderContainer {
      max-width: variables(contentMaxWidth) - 2 * variables(largeGutter);
      flex-direction: row;
      justify-content: flex-start;
      align-items: flex-start;
      height: 190px;
      padding: 0;
    }
    .headerRight {
      padding-top: 30px;
      width: unset;
      align-items: unset;
    }
    .headerLeft {
      padding-left: variables(largeGutter);
    }
    .desktopTabsContainer {
      display: flex;
      margin-top: auto;
      margin-left: calc(-1 * variables(largeGutter));
    }
    .mobileTabsContainer {
      display: none;
    }
    .backActionButton {
      margin-right: variables(largeGutter);
    }
  }
}
