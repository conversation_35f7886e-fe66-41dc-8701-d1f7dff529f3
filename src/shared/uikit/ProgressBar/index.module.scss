@import '/src/shared/theme/theme.scss';

// TOCHECK  arrowFunction, ;

@layer uikit {
  .progressRoot {
    overflow: hidden;
    width: 100%;
    height: 6px;
    border-radius: 4px;
    background-color: colors(barBg);
    position: relative;
  }

  .isStepsPassedHeight {
    height: 12px;
  }
  .progressValue {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    transition: width 0.5s;
    border-radius: 4px;
  }

  .divider {
    position: absolute;
    top: -1px;
    bottom: -1px;
    width: 1px;
    background: colors(white2040);
    z-index: 1;
  }
  .hide {
    display: none;
  }
}
