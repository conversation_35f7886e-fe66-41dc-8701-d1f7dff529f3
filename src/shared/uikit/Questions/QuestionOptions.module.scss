@import '/src/shared/theme/theme.scss';

@layer organism {
  .root {
    position: relative;
    gap: variables(xLargeGutter) * 0.5;
    padding-bottom: 44px;
    margin-bottom: -44px;
    .label {
      font-size: variables(gutter);
      padding-top: variables(gutter) * 0.25;
    }
    .list {
      gap: variables(xLarge<PERSON>utter) * 0.5;
    }
    .btns {
      flex-direction: row;
      gap: variables(gutter) * 0.25;
    }
  }
}
