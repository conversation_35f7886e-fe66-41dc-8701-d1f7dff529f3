@import '/src/shared/theme/theme.scss';

@layer uikit {
  $arrowWidth: 12px;

  .tooltip {
    color: colors(tooltipText);
    background-color: colors(background11);
    padding: 4px 8px;
    border-radius: 4px;
    z-index: zIndex(overlay) + 1;

    :global {
      & .tooltip-arrow {
        position: absolute;
        display: flex;

        &::before {
          content: '';
          display: inline-block;
          width: $arrowWidth;
          height: $arrowWidth;
          background-color: colors(background11);
          transform: rotate(45deg);
        }
      }
    }

    &[data-popper-placement*='right'] {
      margin-left: $arrowWidth !important;

      :global {
        & .tooltip-arrow {
          left: 0;
          margin-left: (-1 * $arrowWidth) * 0.5 + 1px;
        }
      }
    }

    &[data-popper-placement*='left'] {
      margin-right: $arrowWidth !important;

      :global {
        & .tooltip-arrow {
          right: 0;
          margin-right: (-1 * $arrowWidth) * 0.5 + 1px;
        }
      }
    }

    &[data-popper-placement*='right'] .tooltip-arrow {
      :global {
        margin-top: 1px;
      }
    }

    &[data-popper-placement*='left'] .tooltip-arrow {
      :global {
        margin-top: 1px;
      }
    }

    &[data-popper-placement*='top'] {
      margin-bottom: $arrowWidth !important;

      :global {
        & .tooltip-arrow {
          bottom: 0;
          margin-bottom: (-1 * $arrowWidth) * 0.5 + 1px;
        }
      }
    }

    &[data-popper-placement*='bottom'] {
      margin-top: $arrowWidth !important;

      :global {
        & .tooltip-arrow {
          top: 0;
          margin-top: (-1 * $arrowWidth) * 0.5 + 1px;
        }
      }
    }
  }

  .text {
    white-space: nowrap;
  }
}
