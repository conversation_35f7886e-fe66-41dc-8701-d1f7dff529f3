@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .root {
    padding: variables(largeGutter);
    background-color: colors(gray_5);
    gap: variables(largeGutter);
    border-radius: variables(largeGutter) * 0.5;
    border: 1px solid colors(techGray_20);

    .titleBox {
      flex-direction: row;
      align-items: center;

      .titleButtons {
        flex-direction: row;
        align-items: center;
        margin-left: auto;
      }
    }
  }

  .inputs {
    flex-direction: column;
    flex: 1;
    flex-wrap: nowrap;
    gap: variables(xLargeGutter) * 0.5;

    .item {
      flex-basis: unset !important;
    }
  }
  .inputAddon {
    background-color: colors(darkSecondary_hover) !important;
    min-width: 220px !important;
  }

  .formItemWrapStyle {
    flex-basis: content !important;
  }

  .addonIcon {
    padding: variables(gutter) * 0.25 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
}
