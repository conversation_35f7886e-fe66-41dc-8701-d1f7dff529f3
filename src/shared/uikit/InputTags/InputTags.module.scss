@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .inputTagsContainer {
    font-family: fonts(regular);
  }

  .disableInput {
    display: none;
  }

  @each $color-name in map-keys($theme-colors) {
    .borderColor-#{"" + $color-name} {
      border-color: colors($color-name);
    }
  }
  @each $color-name in map-keys($theme-colors) {
    .hoverBorderColor-#{"" + $color-name} {
      &:hover {
        border-color: colors($color-name);
      }
    }
  }

  .inputWrapper {
    border-width: 1px;
    border-style: solid;
    position: relative;
    border-radius: 4px;
    background: colors(background);
    min-height: 56px;
    width: 100%;
    justify-content: center;
  }

  .disabled {
    pointer-events: none;
    user-select: none;
    background-color: colors(hover_75_hover);
  }

  .inputInnerWrapper {
    flex-direction: row;
    padding-right: 22px;
    margin-top: variables(xLargeGutter);
  }

  .label {
    position: absolute;
    top: variables(gutter);
    left: 0;
    right: 0;
    padding-left: variables(gutter);
    padding-top: 2px;
    z-index: 1;
    transition: all 150ms ease;
    font-size: 15px;
  }

  .defaultActive {
    font-size: 16px;
    top: 14px;
  }

  .active {
    font-size: 12px;
    top: 0;
  }

  .helperText {
    margin-top: 6px;
  }

  .errorText {
    color: colors(error);
  }

  .inputTagsContainer :global {
    .react-tagsinput {
      display: inline-flex;
      flex-direction: row;
      overflow: hidden;
      padding: 0 variables(gutter) * 0.5 variables(gutter) * 0.5 variables(gutter);
      flex-grow: 1;
      & > * {
        display: flex;
        flex: 1;
        flex-direction: row;
        align-items: flex-start;
        flex-wrap: wrap;
      }
      & > span {
        gap: variables(gutter) * 0.25;
      }
    }
    .react-tagsinput-tag {
      display: inline-flex;
      flex-direction: row;
      background-color: colors(techGray_20);
      border-radius: 2px;
      color: colors(primaryText);
      font-size: 15px;
      font-weight: 700;
      line-height: 21px;
      padding: 6px variables(xLargeGutter) * 0.5;
      align-items: center;
    }
    .react-tagsinput-remove {
      display: inline-flex;
      flex-direction: row;
      cursor: pointer;
      font-weight: bold;
    }
    .react-tagsinput-tag a::before {
      display: inline-flex;
      flex-direction: row;
      content: '×';
      margin-left: variables(largeGutter) * 0.5;
      font-size: 18px;
      color: colors(primaryText);
    }
    .react-tagsinput-input {
      color: colors(thirdText);
      min-width: 250px;
      flex-grow: 1;
      outline: none;
      font-size: 16px;
      font-weight: 400;
      height: 25px;
      background-color: colors(transparent);
      padding: 0;
      margin-bottom: 5px;
    }
  }

  .empty :global {
    .react-tagsinput {
      padding-bottom: 0;
    }
  }

  .grow {
    flex: 1;
  }
}
