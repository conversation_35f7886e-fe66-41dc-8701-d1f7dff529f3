@import '/src/shared/theme/theme.scss';

@layer uikit {
  .switchRoot {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1;
    padding: variables(gutter) * 0.5 0;
  }
  .errorBoundary {
    color: colors(error);
  }
  .toggle {
    cursor: pointer;
    display: inline-block;
  }
  .toggleSwitch {
    display: inline-block;
    background: colors(primaryDisabledText);
    border-radius: variables(gutter);
    width: variables(largeGutter) * 2;
    height: variables(largeGutter);
    position: relative;
    vertical-align: middle;
    transition: background 0.25s;
    &:after {
      content: '';
    }
    &:before {
      content: '';
      display: block;
      background-color: colors(white);
      border-radius: 50%;
      width: variables(gutter);
      height: variables(gutter);
      position: absolute;
      top: 2px;
      left: 2px;
      transition: left 0.25s;
    }
    &.disabled {
      opacity: 0.3;
    }
  }
  .toggleSwitchActive {
    background-color: colors(brand_trench);

    &:before {
      left: 22px;
    }
  }

  .toggleCheckbox {
    position: absolute;
    clip: rect(0, 0, 0, 0);
  }
}
