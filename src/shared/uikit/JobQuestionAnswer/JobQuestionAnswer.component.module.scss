@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .container {
    margin-top: variables(gutter);
    &:first-child {
      margin-top: variables(gutter) * 0.25;
    }
  }
  .previewContainer {
    margin-top: variables(gutter) * 0.5;
  }
  .question {
    margin-bottom: variables(gutter) * 0.5;
    color: colors(smoke_coal);
  }
  .previewQuestion {
    margin-bottom: variables(gutter) * 0.25 !important;
    color: colors(colorIconForth2);
  }
  @media (min-width: breakpoints(tablet)) {
    .question {
      margin-bottom: 8px;
    }
    .container {
      margin-top: variables(largeGutter);
      &:first-child {
        margin-top: variables(gutter) * 0.5;
      }
    }
    .previewContainer {
      margin-top: 12px;
    }
  }
}
