@import '/src/shared/theme/theme.scss';

@font-face {
  font-family: 'ellipsis-font';
  src: local('helvetica'), local('courier'), local('times');
  unicode-range: U+2026;
  size-adjust: 0%;
}

@layer uikit {
  .mainContainer {
    width: 100%;
    position: relative;
  }
  .outerWrapper {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    //font-family: 'ellipsis-font', fonts(regular);
    //& span {
    //  font-family: fonts(regular) !important;
    //}
  }
  .richTextViewContainer {
    height: auto;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: initial !important;
    & *::selection {
      color: colors(white);
      background: colors(brand);
    }

    :global {
      & .mention {
        height: auto;
        line-height: initial;
        background-color: unset !important;
        padding: 0;
        margin: 0;
        user-select: auto;
        display: inline;
        color: colors(brand);
        font-family: fonts(bold);
        font-weight: bold;
      }

      & .mention > span {
        margin: unset !important;
        display: inline-block;
      }
      & .ql-mention-denotation-char {
        margin: unset !important;
        display: inline-block;
      }
    }
    & [data-is-reply-to-mention] {
      color: colors(brand);
      display: flex;
      align-items: center;
      width: 100%;

      & > span {
        font-family: fonts(regular) !important;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 100%;
      }
    }
  }
  .richTextWithMentionHover {
    :global {
      & .mention {
        cursor: pointer;
        @media (min-width: breakpoints(tablet)) {
          @include underlineAnimation;
        }

        &:after {
          content: ' ';
          pointer-events: none;
          user-select: none;
        }
      }
      & .richTextAnchor {
        white-space: pre-wrap;
        word-break: break-all;
        color: colors(brand);
        @include underlineAnimation;
      }
    }
  }
  .richTextWithMentionClick {
    & span[data-denotation-char] > span {
    }
    & .richTextAnchor {
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  .seeMoreContainer {
    justify-content: flex-end;
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .readMoreRoot {
    display: inline;
    & * {
      display: inline;
    }
  }
  .readMoreBtn {
    display: inline;
    & * {
      display: inline;
    }
  }
  .readMoreText {
    white-space: nowrap;
    padding-left: 5px;
    line-height: initial;
  }
  .buttonWrapper {
    display: inline;
  }

  @media (min-width: breakpoints(tablet)) {
    .richTextViewContainer {
      line-height: inherit;
    }
  }
}
