@import '/src/shared/theme/theme.scss';

.richTextEndAnchor {
  position: absolute;
  bottom: -50px;
}

.fontContainer {
  display: flex;
  flex-direction: row;
  transition:
    margin-left 0.3s ease,
    margin-top 0.3s ease,
    padding-bottom 0.3s ease;
}

.showOnRight {
  margin-left: auto;
  margin-top: -32px;
  margin-bottom: variables(gutter) * 0.5;
}

.showOnBottom {
  margin-top: auto;
  padding-bottom: 8px;
}

.emojiPickerClassName {
  display: none;
}

.row {
  flex-direction: row;
  gap: variables(gutter) * 0.5;
}

.commentInputStyle {
  border: 1px solid colors(borderSixth);
  border-radius: variables(largeGutter);
  padding-left: variables(gutter);
  padding-right: variables(largeGutter) + variables(gutter) * 0.25;

  :global {
    & .ql-editor {
      padding: 7px 0;
    }
  }

  &.focused {
    border: 1px solid colors(borderSeventh);
  }
}

.helperText {
  margin-left: variables(gutter);
  margin-top: 4px;
}

.errorText {
  color: colors(error);
}

.maxLength {
  margin-left: auto;
  margin-top: 4px;
}

.richTextEditor {
  position: relative;
  font-family: fonts(regular);
  pointer-events: auto;
  opacity: 1;
  user-select: auto;
  word-break: break-word;
  caret-color: colors(thirdText);
  max-height: 320px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &::selection {
    color: colors(white);
    background: colors(brand);
  }

  :global {
    & .mention {
      height: 24px;
      border-radius: 6px;
      background-color: unset;
      padding: 0;
      margin-right: 0;
      user-select: auto;
      display: inline;
      color: inherit;
      letter-spacing: 0.02em;
      font-family: fonts(bold);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
      font-feature-settings:
        'pnum' on,
        'lnum' on,
        'liga' off;

      & *::selection {
        color: colors(white);
        background: colors(brand);
      }

      & > span {
        margin: 0 0 !important;
      }
    }

    & .ql-container {
      overflow: auto;
      font-size: 16px;

      :global {
        & #editor-context-menu {
          background-color: colors(techGray_20);
          color: white;
          padding: variables(gutter) * 0.5;
          font-size: 14px;
          gap: variables(gutter) * 0.25;
          display: flex;
          flex-direction: column;
          border-radius: variables(gutter) * 0.25;
        }
      }
    }

    & .hash_tag {
      color: red;
    }

    & .ql-blank {
      font-size: var(--placeholderFontSize, 16px);
    }

    & .ql-blank p {
      padding-left: 0;
    }

    & .ql-editor p {
      color: colors(thirdText);
      padding-left: 0;
      transition: all 0.1s linear;
      font-weight: 400;

      &::selection {
        color: colors(white);
        background: colors(brand);
      }
    }

    // placeholder color
    & .ql-blank::before {
      color: colors(smoke);
      left: 0;
    }

    & .ql-editor,
    & .ql-editor > p {
      font-weight: normal;
      line-height: 24px;
      font-family: fonts(regular);
      display: block;
      letter-spacing: 0.02em;
      padding-left: 0;
    }

    & .ql-editor.ql-blank::before {
      content: attr(data-placeholder);
      font-style: normal;
      font-family: fonts(regular);
      left: 0;
      font-weight: 400;
      pointer-events: none;
      position: absolute;
      right: 15px;
      white-space: nowrap;
    }

    & .ql_arrow_top {
      width: 55px;
      height: 31px;
      position: relative;
      display: flex;
      overflow: hidden;
      box-shadow: 0 16px 10px -17px rgb(0 0 0 / 20%);
    }

    & .ql_arrow_top:after {
      content: '';
      position: absolute;
      width: 15px;
      height: 15px;
      background-color: colors(background);
      z-index: 999999;
      transform: rotate(45deg);
      top: 25px;
      left: 25px;
      box-shadow: 2px 4px 15px 0 rgba(0, 0, 0, 0.3);
    }

    & .ql-tooltip {
      display: none;
    }

    & .rich-text-icon {
      font-size: 20px;
      font-weight: 400;
      color: colors(colorIconSecond);
      cursor: pointer;
    }
  }

  &_isModalView {
    max-height: 30vh;
  }
}

.richTextEditorDisabled {
  user-select: none;
  pointer-events: none;
}
.richTextEditorDisabled:not(.richTextEditorDisabledReadOnly) {
  opacity: 0.5;
}
.richTextEditorDisabledReadOnly {
  background-color: colors(hover_75_hover);
}
.shortText {
  :global {
    .ql-editor {
      padding-block: 4px;
    }
    & .ql-editor p {
      font-size: 24px;
    }
  }
}

.longText {
  :global {
    & .ql-editor p {
      font-size: 16px !important;
    }
  }
}

.bell {
  color: colors(error);
  font-size: 20px;
}

.toggle {
  position: absolute;
  right: variables(gutter) + 6;
  top: 11px;
  justify-content: center;
  align-items: center;
  margin: auto 0;
  // z-index: 1;
}

.cursorDefault {
  cursor: default;
}

.floatingLabel {
  pointer-events: none;
  position: absolute;
  transform: translateY(-15px) scale(1);
  transform-origin: left top;
  transition: 100ms;
}

.floatingLabelFocus {
  transform: translateY(-24px) scale(0.9);
  color: colors(brand);
  width: 100%;
}

.truncatedText {
  width: calc(100% - #{variables(gutter) * 2});
}

.borderStyle {
  min-height: 79px;
  border: 1px solid colors(techGray_20);
  border-radius: 4px;
  padding: 32px 16px 8px 16px;
  transition: border-color 0.3s ease;

  &:hover {
    border: 1px solid colors(secondaryDisabledText);
  }

  &:focus {
    border: 1px solid colors(brand);
  }

  :global {
    & .ql-editor {
      padding: 0;
      white-space: normal !important;
    }

    & .ql-editor p {
      line-height: 24px;
    }
  }
}

.focusedBorder {
  border-color: colors(brand) !important;
}

.errorBorder {
  border-color: colors(error);
}

.readOnlyStyle {
  min-height: unset;
  height: unset;
  border: unset;
  border-radius: 0;
  padding: 0;

  & .ql-editor {
    padding: 0 !important;
  }

  & .mention {
    cursor: pointer;
  }
}

.mentionContainer {
  // Additional global styles
  background-color: colors(background);
  max-height: 330px;
  border: 1px solid colors(transparent);
  border-radius: 0;
  z-index: 9001;
  overflow: auto;
  box-shadow: 2px 4px 15px 0 rgba(0, 0, 0, 0.1);
  padding: variables(gutter) * 0.5;
  right: 0 !important;
  width: unset !important;
  box-shadow: unset;
  border-top: 1px solid colors(techGray_20);
  border-bottom: 1px solid colors(techGray_20);
  z-index: 1050;

  &:before {
    content: '';
    position: absolute;
    right: 11px;
    top: -10px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 10px 10px 10px;
    z-index: 9999;
  }

  :global {
    .avatar-square-rounded {
      border-radius: 4px;
    }

    .avatar-circle-rounded {
      border-radius: 200px;
    }

    .mention-avatar-empty-root {
      width: 40px;
      height: 40px;
      min-width: 40px;
      min-height: 40px;
      margin-right: variables(gutter) * 0.5;
    }

    .mention-avatar-empty {
      width: 100%;
      height: 100%;
      overflow: hidden;
      min-width: 100%;
      min-height: 100%;
      align-items: center;
      padding-top: 5.05px;
      border-radius: 50%;
      justify-content: center;
      background-color: colors(skeletonBg);
    }

    #tab-toolbar ul {
      display: block;
    }

    #tab-panel {
      flex-direction: row;
    }

    #tab-panel .ap {
      margin: 2px;
    }

    .emoji-mart-search {
      display: none;
    }

    .mention > span {
      margin: unset !important;
      display: inline;

      &::selection {
        color: colors(white) !important;
        background: colors(brand) !important;
      }
    }

    .ql-mention-list-item {
      background-color: unset;
      text-decoration: none;
      padding: unset;
      cursor: pointer;
      line-height: 44px;
      font-size: 16px;
      vertical-align: middle;
      margin-bottom: variables(gutter) * 0.25;

      &:last-child {
        margin-bottom: 0;
      }

      &.selected {
        background-color: unset;
      }
    }

    .ql-mention-denotation-char {
      margin: unset !important;
      display: inline-block;

      &::selection {
        color: colors(white) !important;
        background: colors(brand) !important;
      }
    }

    .ql-mention-loading {
      line-height: 44px;
      padding: 0 20px;
      vertical-align: middle;
      font-size: 16px;
    }

    .ql-mention-list {
      list-style: none;
      margin: 0;
      padding: 0;
      overflow: auto;
    }

    .ql-mention-list-container {
    }

    .qlm_list {
      height: 300px !important;
      justify-content: center;
      flex-direction: column;
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -moz-box-flex: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
    }

    .qlm_listItem {
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      flex-direction: row;
      padding: variables(gutter) * 0.5;
      align-items: center;
      border-radius: 4px;
      height: 65px;

      &:hover {
        background-color: colors(hoverPrimary);
      }

      & .qlmh_avatar_container {
        fill: colors(brand);
        margin-right: variables(gutter) * 0.5;
      }
    }

    .qlmh_listItem {
      height: unset;
    }

    .qlm_text {
      justify-content: space-between;
      font-size: 16px;
      color: rgb(7, 34, 82);
      font-family: fonts(bold);
      font-style: normal;
      font-weight: normal;
      line-height: 22px;
    }

    .qlm_secondary_text {
      font-size: 13px;
      color: colors(colorIconForth2);
      font-family: fonts(regular);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 300px;
      align-items: flex-start;
      line-height: 15px;
    }

    .qlmh_secondary_text {
      font-size: 12;
    }

    .qlm_item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .qlm_avatar_container {
      justify-content: center;
    }

    .qlm_avatar {
      width: 40px;
      height: 40px;
      margin-right: variables(gutter) * 0.5;
    }

    .qlm_value {
      font-family: fonts(regular);
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 18px;
      display: flex;
      align-items: center;
      letter-spacing: 0.02em;
      color: colors(smoke_coal);
      align-self: flex-start;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 170px;
      align-items: flex-start;
      margin-bottom: variables(gutter) * 0.25;
    }

    .qlm_hashtag .qlm_value {
      font-weight: 400;
      font-size: 16px;
      line-height: 21px;
    }

    .qlmh_value {
      font-size: 15px;
      margin-bottom: 0 !important;
    }

    .qlm_type {
      font-weight: 400;
      font-size: 14;
      color: colors(secondaryDisabledText);
      line-height: 17px;
    }

    .qlm_hashtag .qlm_listItem {
      height: 40px;
    }
  }
}

.mentionContainerLeftZero {
  left: 0 !important;
}

.mentionContainerInFooter {
  position: fixed !important;
  bottom: var(--inFooterHeight, 0) !important;
  top: unset !important;
  border-bottom: unset !important;
  width: var(--inFooterWidth, 0) !important;
  right: unset !important;
  left: var(--inFooterleft, 0) !important;
  border-top: 1px solid colors(techGray_10);
  border-bottom: unset;
}

.commentMention {
  width: 100%;
}

@media (min-width: breakpoints(tablet)) {
  .mentionContainerDesktop {
    border-radius: 12px;
    width: 270px !important;
    border: 1px solid colors(techGray_20);
    visibility: hidden;
  }
  .noDisplay {
    display: none;
  }
  .emojiPickerClassName {
    display: flex;
  }
}
