@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .wrapper {
    position: relative;
  }

  .inputRoot {
    width: 100%;
    position: relative;
    font-family: fonts(regular);
    height: 32px;
    min-height: 32px;
    overflow: hidden;
    transition: 0.4s;
    z-index: 2;
    margin-left: auto;
  }
  .inputRootLarge {
    height: 40px;
    min-height: 40px;
  }

  .input {
    height: 100%;
    font-size: 16px;
    padding: 10px 10px 10px 16px;
    background: colors(background);
    color: colors(smoke_coal);
    border-radius: 20px;
    border: 1px solid colors(techGray_20);
    width: 100%;
    transition: border-color 0.3s ease;

    &:hover {
      border: 1px solid colors(secondaryDisabledText) !important;
    }

    &:focus {
      border: 1px solid colors(brand) !important;
    }

    &::-webkit-input-placeholder {
      color: colors(inputPlaceholder);
    }

    &::-moz-placeholder {
      color: colors(inputPlaceholder);
    }

    &::-ms-input-placeholder {
      color: colors(inputPlaceholder);
    }

    &::placeholder {
      color: colors(inputPlaceholder);
    }

    background-clip: padding-box;

    &::selection {
      color: colors(white);
      background: colors(brand);
    }
  }

  .inputSquare {
    border-radius: 4px;
  }
  .inputActive {
    border-color: colors(borderSeventh);
  }
  .rightIconsWrapper {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    flex-direction: row;
    align-items: center;
    gap: var(--8_12);
  }
  .clean {
    transition: transitions(smooth);
    border-radius: 16px;
    z-index: 1;

    &:hover {
      background-color: colors(hoverPrimary);
    }
  }
  .cleanLarge {
    top: 4px;
  }
  .searchIcon {
    color: colors(colorIconTen);
  }
  .searchIconLarge {
    top: 4px;
    right: 4px;
  }
}
