@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .radioGroupRoot {
    flex-direction: row;
    align-items: center;
    flex-grow: 1;
    padding: variables(gutter) * 0.5;
    border-radius: 4px;
    transition: transitions(smooth);
  }

  .radioGroupLabelWrapper:not(:last-child) {
    flex-grow: 1;
  }

  .itemWrapper {
    margin-bottom: variables(gutter) * 0.5;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .tooltipContent {
    padding: 8px;
    text-wrap: wrap;
    max-width: 180px;
    border-radius: 8px;
  }

  .icon {
    margin-right: variables(gutter) * 0.5;
    align-self: flex-start;
    & > span {
      font-size: 20px;
      color: colors(primaryDisabledText);
    }
  }

  .selectedIcon {
    & > span {
      font-size: 20px;
      color: colors(brand);
    }
  }

  .header {
    padding: variables(gutter) * 0.25 variables(gutter) * 0.5 variables(gutter);
    border-bottom: 1px solid colors(techGray_10);
  }

  .overflowHidden {
    overflow: hidden;
  }
}
