@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .container {
    padding: variables(largeGutter) variables(gutter);
    border: 1px solid colors(border);
    position: relative;
    border-radius: 4px;
  }
  .slider {
    width: 100%;
    flex-direction: row;
    height: 12px;
    border-radius: 4px;
    background-color: unset;
  }
  .thumbClassName {
    background-color: colors(brand);
    width: 25px;
    height: 25px;
    border-radius: 50px;
    top: 50%;
    transform: translate(0%, -50%);
    transition:
      left 0.05s linear,
      right 0.05s linear;
  }
  .trackClassName {
    background: unset;
    border-radius: 4px;
    height: 100%;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer !important;
    &:nth-child(1) {
      background-color: #cbdefc;
    }
    &:nth-child(2) {
      background: colors(barProgress);
    }
    &:nth-child(3) {
      background-color: #cbdefc;
    }
    transition:
      left 0.05s linear,
      right 0.05s linear;
  }
  .labelsWrapper {
    margin-top: variables(gutter);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .label {
    flex-direction: row;
    align-items: center;
  }
  .marker {
    width: 1px;
    height: 100%;
    background-color: colors(white);
    cursor: pointer;
  }
}
