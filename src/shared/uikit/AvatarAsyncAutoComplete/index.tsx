import React from 'react';
import AsyncAutoComplete from '../AutoComplete/AsyncAutoComplete';
import AvatarCard from '../AvatarCard';
import cnj from '../utils/cnj';
import classes from './index.module.scss';
import type { ComponentProps } from 'react';

const AvatarAsyncAutoComplete: React.FC<
  ComponentProps<typeof AsyncAutoComplete> & { isCompany?: boolean }
> = (props) => {
  const { value, isCompany } = props;
  const image = value?.image;

  return (
    <AsyncAutoComplete
      renderItem={({ item }: any) => (
        <AvatarCard
          data={{
            title: item.label,
            subTitle: `@${item.helperText}`,
            image: item.image,
          }}
          avatarProps={{ isCompany, size: 'sm', name: item.label }}
          subTitleProps={{ className: cnj(classes.tagName) }}
        />
      )}
      {...props}
      leftIcon={
        <AvatarCard
          data={{ image }}
          avatarProps={{ isCompany, name: value?.label }}
          containerProps={{ className: classes.avatarBox }}
        />
      }
      inputWrapClassName={classes.inputWrapClassName}
      textInputProps={{
        leftIconClassname: classes.avatarBoxWrapper,
        labelClassName: classes.labelClassName,
        ...props.textInputProps, // spread rest of the textInputProps
      }}
      inputStyle={classes.inputStyle}
    />
  );
};

export default AvatarAsyncAutoComplete;
