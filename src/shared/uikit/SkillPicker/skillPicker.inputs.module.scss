@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .inputsContainer {
  }
  .levelDropDown {
    margin-top: variables(gutter) * 0.5;
  }
  .button {
    width: 100%;
    &:nth-child(1) {
      margin-right: variables(xLargeGutter) * 0.25;
    }
    &:nth-child(2) {
      margin-left: variables(xLarge<PERSON>utter) * 0.25;
    }
  }
  .buttonsWrapper {
    margin-top: variables(xLargeGutter) * 0.5;
    flex-direction: row;
  }
}
