@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .wrapper {
    position: relative;
  }

  .inputRoot {
    width: 100%;
    position: relative;
    font-family: fonts(regular);
    height: 32px;
    min-height: 32px;
    overflow: hidden;
    transition: 0.4s;
    z-index: 2;
    margin-left: auto;
  }
  .inputRootLarge {
    height: 40px;
    min-height: 40px;
  }
  .widthZero {
    width: 0;
  }

  .input {
    height: 100%;
    font-size: 16px;
    padding: 10px 10px 10px 16px;
    background: colors(background);
    color: colors(smoke_coal);
    border-radius: 20px;
    border: 1px solid colors(techGray_20);
    width: 100%;
    transition: border-color 0.3s ease;
    &:hover {
      border-color: colors(secondaryDisabledText);
    }

    &:focus {
      border: 1px solid colors(brand);
    }

    &::-webkit-input-placeholder {
      color: colors(colorIconForth2);
    }

    &::-moz-placeholder {
      color: colors(colorIconForth2);
    }

    &::-ms-input-placeholder {
      color: colors(colorIconForth2);
    }

    &::placeholder {
      color: colors(colorIconForth2);
    }

    background-clip: padding-box;

    &::selection {
      color: colors(white);
      background: colors(brand);
    }
  }
  .inputSquare {
    border-radius: 4px;
  }
  .inputActive {
    border-color: colors(borderSeventh);
  }

  .clean {
    position: absolute;
    top: 0;
    line-height: 14;
    right: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: transitions(smooth);
    border-radius: 16px;

    &:hover {
      background-color: colors(hoverPrimary);
    }
  }

  .cleanLarge {
    top: 4px;
  }
  .searchIcon {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    color: colors(colorIconTen);
    width: 32px;
    height: 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: transitions(smooth);
    border-radius: 16px;
  }
  .searchIconLarge {
    top: 4px;
    right: 4px;
  }

  .visibleClean {
    display: flex;
  }

  .toggleSearch {
    position: absolute;
    right: 0;
    z-index: 1;
    cursor: pointer;
    opacity: 1;
    transition: 0.4s;
    height: 32px;
    width: 54px;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    color: colors(colorIconTen);
    border: 1px solid colors(borderSixth);
  }

  .toggleSearchIsOpen {
    z-index: 3;
    opacity: 0;
  }
  .toggleSearchLarge {
    height: 40px;
  }
  .toggleVariantWidth {
    width: auto;
  }
  .noBorder {
    border: none;
  }
}
