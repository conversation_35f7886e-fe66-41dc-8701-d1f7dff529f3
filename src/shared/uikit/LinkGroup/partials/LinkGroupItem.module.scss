@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .root {
    padding: variables(largeGutter);
    background-color: colors(gray_5);
    gap: variables(largeGutter);
    border-radius: variables(largeGutter) * 0.5;
    border: 1px solid colors(techGray_20);

    .titleBox {
      flex-direction: row;
      align-items: center;

      .titleButtons {
        flex-direction: row;
        align-items: center;
        margin-left: auto;
      }
    }
  }

  .inputs {
    flex-direction: column;
    flex: 1;
    flex-wrap: nowrap;
    gap: variables(xLargeGutter) * 0.5;

    .item {
      flex-basis: unset !important;
    }
  }
}
