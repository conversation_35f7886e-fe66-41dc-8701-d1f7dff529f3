@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .carousel {
    white-space: nowrap;
    overflow: hidden;
    padding-bottom: variables(gutter);
    height: fit-content;
  }
  .userPickerSelectedRoot {
    flex-direction: row;
    overflow: hidden;
    white-space: nowrap;
    scrollbar-width: none;
    padding-left: 0;
    padding-bottom: variables(gutter);
  }
  .container {
    padding: variables(gutter) * 0.5;
    min-width: 230px;
    margin-left: 0;
    margin-top: 0;
    margin-right: variables(gutter) * 0.5;
    border: 1px solid colors(borderSixth);
    width: 300px;

    &:hover {
      border-color: colors(brand);
    }
    &:last-child {
      margin-right: variables(gutter);
    }
  }

  .action {
    margin-left: auto;
  }
  .title {
    word-break: break-all;
  }

  @media (min-width: breakpoints(tablet)) {
    .carousel {
      padding-bottom: 0;
    }

    .userPickerSelectedRoot {
      padding-bottom: variables(largeGutter);
      padding-left: variables(largeGutter);
      max-width: 648px;
    }
    .container {
      min-width: 250px;
      &:last-child {
        margin-right: variables(largeGutter);
      }
    }
    .action {
      margin-left: auto;
    }
  }
}
