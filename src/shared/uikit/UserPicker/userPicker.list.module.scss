@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .list {
    padding: 0 variables(gutter) * 0.5 variables(gutter);
  }

  .action {
    margin-left: auto;
  }
  .containerProps {
    padding: variables(xLargeGutter) * 0.5 variables(gutter) * 0.5;
  }
  .infoWrapUserPickerList {
    overflow: hidden;
  }
  .bgBrand {
    background-color: colors(brand_10);
  }

  @media (min-width: breakpoints(tablet)) {
    .list {
      padding: 0 variables(xLargeGutter) * 0.5 variables(largeGutter);
    }
  }
}
