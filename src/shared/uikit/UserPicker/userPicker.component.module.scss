@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .suggestion {
    margin: variables(gutter) 0 12px 0;
    padding: 0 variables(gutter);
  }
  .container {
    padding: 0 variables(gutter);
  }
  .searchInput {
    flex: 0;
    padding: 0 variables(gutter);
    margin-bottom: variables(gutter) * 0.5;
  }
  .countText {
    margin-top: variables(gutter) * 0.5;
    margin-bottom: variables(gutter);
    margin-right: (5 * variables(gutter)) * 0.25;
    place-self: end;
  }
  .listWrapper {
    min-height: 300px;
    overflow-y: auto;
    max-height: calc(100vh - 112px);
  }
  .listWrapperWithSelected {
    padding-bottom: 80px;
  }
  .alert {
    justify-content: center;
    align-items: center;
    height: variables(desktopGutter);
    padding: 0 12px;
    margin: variables(gutter) * 0.5 variables(largeGutter) 0
      variables(largeGutter);
  }
  .emptyMsg {
    margin-top: variables(gutter);
    padding: 0 variables(gutter);
  }
  .itemWrapper {
    margin-bottom: variables(gutter);
  }

  @media (min-width: breakpoints(tablet)) {
    .searchInput {
      padding: 0 variables(largeGutter);
      margin-bottom: variables(xLargeGutter) * 0.5;
    }
    .suggestion {
      margin: variables(largeGutter) 0;
      padding: 0 variables(largeGutter);
    }
    .container {
      padding: 0 variables(largeGutter);
    }
    .spinner {
      margin-top: variables(largeGutter);
    }
    .emptyMsg {
      margin-top: variables(largeGutter);
      padding: 0 variables(largeGutter);
    }
    .countText {
      margin-right: 2 * variables(gutter);
    }
    .listWrapper {
      height: auto;
      padding-bottom: 0;
      flex: 1;
      max-height: calc(100vh - 280px);
    }
    .itemWrapper {
      margin-bottom: variables(largeGutter);
    }
  }
}
