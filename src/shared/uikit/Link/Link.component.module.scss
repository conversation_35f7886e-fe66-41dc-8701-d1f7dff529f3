@import '/src/shared/theme/theme.scss';

@layer uikit {
  .linkHashRoot {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    &:focus,
    &:hover,
    &:visited,
    &:link,
    &:active {
      text-decoration: none;
      outline: none;
    }
  }
  .showUnderline {
    text-decoration: default;
    &:focus,
    &:hover,
    &:visited,
    &:link,
    &:active {
      text-decoration: default;
      outline: default;
    }
  }
}
