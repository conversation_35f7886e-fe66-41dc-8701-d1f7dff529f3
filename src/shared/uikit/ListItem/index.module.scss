@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .leftIconContainer {
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
    margin-top: 2px;
  }

  .container {
    display: flex;
    flex-direction: column;
    width: 189.89px;
  }

  .label {
    flex-grow: 1;
    text-align: left;
    margin-left: 10px;
    white-space: nowrap;
    line-height: 20px;
  }

  .listItemRoot {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 12px 12px;
    border-radius: 4px;
    background: colors(background);
  }

  .listItemRootPopper {
    padding: 12px 20px;
    border-radius: 0;
  }

  .hover {
    cursor: pointer;
    transition: transitions(smooth);
    &:hover {
      border-radius: 4px;
    }
  }

  .disabled {
    background-color: transparent !important;
    opacity: 0.3;
    cursor: initial;
  }

  @each $color-name in map-keys($theme-colors) {
    .hoverBg-#{"" + $color-name} {
      &:hover {
        background-color: colors($color-name);
      }
    }
  }

  @each $color-name in map-keys($theme-colors) {
    .hoverColor-#{"" + $color-name} {
      &:hover {
        & .rightIcon {
          color: colors($color-name);
        }
        & .leftIcon {
          color: colors($color-name);
        }
        & .listItemLabel {
          color: colors($color-name);
        }
      }
    }
  }

  .secondaryLabel {
    flex-grow: 1;
    text-align: left;
    margin-left: 10px;
    white-space: nowrap;
    line-height: 21px;
  }

  .narrowSituation {
    display: block;
  }
  .narrowSituationWide {
    display: none;
  }

  @media (min-width: breakpoints(tablet)) {
    .listItemRoot {
      padding: 10px;
      border-radius: 4px;
      &:hover {
        border-radius: 4px;
      }
    }
  }

  @media (min-width: breakpoints(midDesktop)) {
    .narrowSituation {
      display: block;
    }
  }
}
