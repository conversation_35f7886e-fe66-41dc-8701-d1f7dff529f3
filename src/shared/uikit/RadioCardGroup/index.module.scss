@import '/src/shared/theme/theme.scss';

@layer organism {
  .button {
    flex-direction: row;
    align-items: center;
    padding: variables(xLargeGutter) * 0.5 variables(gutter) * 0.5;
    border: 1px solid colors(techGray_20);
    border-radius: 4px;
  }
  .grow {
    flex: 1;
  }

  .wrapper {
    gap: variables(xLargeGutter) * 0.5;
  }

  .active {
    background-color: colors(brand_20);
  }

  .divider {
    margin: 0;
  }
  @media (min-width: breakpoints(tablet)) {
  }
}
