@import '/src/shared/theme/theme.scss';

@layer uikit {
  .paperRoot {
    padding: 0;
    background: colors(background);
  }
  .width {
    width: 100%;
  }

  .paperContent {
    flex-direction: column;
    background: colors(background);
    padding: variables(gutter);
    justify-content: space-between;
    transition: transitions(smooth);
    &:hover {
      background-color: colors(hoverPrimary);
      cursor: pointer;
    }
  }
  @each $color-name in map-keys($theme-colors) {
    .hover-backgroundColor-#{"" + $color-name} {
      &:hover {
        background-color: colors($color-name);
      }
    }
  }

  .noHover {
    &:hover {
      background-color: inherit;
      cursor: default;
    }
  }
  .flexDirRow {
    flex-direction: row;
  }
  .flexDirRowReverse {
    flex-direction: row-reverse;
    justify-content: flex-end;
  }
  .flexColRowReverse {
    flex-direction: column-reverse;
  }

  .titleWrap {
    align-self: stretch;
    align-items: flex-start;
    padding-bottom: variables(gutter);
  }
  .titleBorderBottom {
    border-bottom: 1px solid colors(techGray_20);
    padding-bottom: variables(gutter) * 0.5;
  }
  .titleWrapRow {
    border-bottom: none;
    padding-bottom: 0;
  }
  .titleWrapColumnReverse {
    border-bottom: none;
    align-items: center;
    margin-top: 6px;
    padding-bottom: 0;
  }
  .titleWrapRowReverse {
    border-bottom: none;
    padding-bottom: 0;
  }

  @media (min-width: breakpoints(tablet)) {
    .paperRoot {
      padding: variables(largeGutter);
      border-radius: 12px;
    }

    .paperContent {
      border-radius: 12px;
      padding: variables(largeGutter);
    }
    .titleWrap {
      padding-bottom: variables(largeGutter);
    }
    .titleBorderBottom {
      border-bottom: 1px solid colors(techGray_20);
      padding-bottom: variables(largeGutter) * 0.5;
    }
    .titleWrapRowReverse {
      border-bottom: none;
      padding-bottom: 0;
    }
  }
}
