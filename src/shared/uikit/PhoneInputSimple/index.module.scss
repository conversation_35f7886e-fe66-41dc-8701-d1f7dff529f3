@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .inputWrap {
    position: relative;
    z-index: 100;
    flex-direction: row;
    align-items: center;
    border: 1px solid colors(techGray_20);
    padding-right: variables(gutter);
    border-radius: variables(gutter) * 0.25;
    transition: border-color 0.3s ease;
    &.disabledReadOnly {
      background-color: colors(hover_75_hover);
      pointer-events: none;
      user-select: none;
    }
    &:hover {
      border: 1px solid colors(secondaryDisabledText);
    }
    &:focus-within {
      border: 1px solid colors(brand);
    }
  }
  .containerClass {
    position: relative;
    background: colors(background);
    height: auto;
    width: 100%;
    border: none;
    border-width: 0;
  }
  .phoneInputClass {
    padding: 0;
    border: unset;
    background: colors(transparent);
    color: colors(thirdText);
    font-size: 15px;
    line-height: 21px;
    &:disabled {
      cursor: default;
    }
    @media (max-width: breakpoints(tablet)) {
      font-size: 16px;
    }
  }
  .containerClassDisabledReadOnly {
    background: transparent;
    pointer-events: none;
    user-select: none;
  }

  .buttonClass {
    margin-right: 0;
    background: colors(transparent);
    border: unset;
    border-radius: variables(gutter) * 0.25 0 0 variables(gutter) * 0.25;

    &:hover {
      background: colors(darkSecondary_hover);
    }
  }
  .buttonClassPrivateable {
    //margin-right: variables(gutter);
  }
  .dropdownClass {
    z-index: 9;
    top: 50px;
    background: colors(background);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    border-radius: variables(gutter) * 0.25;
    width: var(--phone-country-picker-width, 400px);
  }
  .wrapperClass {
    background-color: colors(transparent);
    border-width: 0;
    &:hover {
      border-width: 0;
    }
  }
  .rightIcon {
    justify-content: center;
    align-items: center;
    margin: auto 0 auto auto;
    z-index: 1;
  }
  .helperText {
    margin-left: variables(gutter);
    margin-top: variables(gutter) * 0.25;
  }
  .errorText {
    color: colors(error);
  }
  .autoCompleteWrap {
    border-color: colors(techGray_20);
    height: auto;
  }
  .autoCompleteInput {
    padding: 19px variables(gutter);
  }
}
