@import '/src/shared/theme/theme.scss';

@layer uikit {
  .containerClass {
    border: unset;
    border-radius: unset;
    background: unset;
    height: unset;
    width: 100%;
  }

  .inputClass {
    font-size: 15px;
    color: colors(thirdText);
    background: colors(transparent);
    height: unset;

    &::selection {
      color: colors(white);
      background: colors(brand);
    }

    &::placeholder {
      color: colors(colorIconForth2);
      font-size: 14px;
      font-weight: 400;
    }
  }

  .buttonClass {
    display: none;
  }

  .dropdownClass {
    display: none;
  }

  .searchClass {
    display: none;
  }

  @media (pointer: coarse) {
    .inputClass {
      &:hover {
        background-color: colors(transparent);
      }

      &:focus {
        background-color: colors(transparent);
      }

      &:active {
        background-color: colors(transparent);
      }

      &:visited {
        background-color: colors(transparent);
      }
    }
  }
}
