@import '/src/shared/theme/theme.scss';

@layer uikit {
  .phoneInputSimple {
    width: 100%;

    .phoneBox {
      display: flex;
      flex-direction: row;
      align-items: stretch;
      width: 100%;
      gap: variables(gutter) * 0.5;

      .flagBox {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 5px;
        padding: variables(gutter);
        padding-right: variables(gutter) * 0.5;
      }

      .phoneSection {
        flex-direction: column;
        width: 100%;
        flex-grow: 1;
        gap: 5px;
        padding: (variables(gutter) * 0.5) 0;

        .phoneTitle {
          transition:
            font-size 0.2s,
            margin 0.2s;
          transform-origin: top left;
          font-size: variables(gutter);
          font-weight: 400;
          line-height: 14px;
          color: colors(disabledGrayDark);
          margin-top: variables(largeGutter);
          margin-bottom: -1 * variables(largeGutter);
        }

        .phoneTitleShrink {
          font-size: variables(xLargeGutter) * 0.5;
          margin-top: 0;
          margin-bottom: 0;
        }

        .InputPhoneStyle {
          flex-grow: 1;
          font-weight: 400;
          font-size: 15px;
          line-height: variables(gutter);

          & > span {
            min-height: initial;
          }
        }

        .wrapperInputPhone {
          border-width: 0;
          height: auto;
        }

        .inputStyle {
          border: unset;
        }
      }

      .option {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: row;
        height: 54px;
        align-items: center;
        justify-content: start;
      }

      .dropDown {
        position: absolute;
        top: calc(100% + 1px);
        right: 0;
        left: 0;
        width: auto;
        overflow-y: auto;
        overflow-x: hidden;
        box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.08);
      }
    }
  }
  .optionsRoot {
    gap: variables(gutter) * 0.25;
    border-top-width: 0px !important;
  }
  .optionItem {
    flex-direction: row;
    align-items: center;
    gap: variables(xLargeGutter) * 0.5;
    height: 58px;
    padding: variables(gutter) * 0.5;
    font-family: var(--font-roboto-regular);
    color: colors(smoke_coal);
  }

  .flagSelect {
    width: variables(logoMinHeight);
    height: variables(logoMinHeight);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    border-radius: 100%;
    background-color: colors(gray_5);
  }

  .flagOption {
    width: 42px;
    height: 42px;
  }
  .autoCompleteWrap {
    padding: variables(gutter);
    background-color: colors(popOverBg_white);
    border-radius: variables(gutter) * 0.25 variables(gutter) * 0.25 0 0;
    border: 1px solid colors(techGray_20);
    border-bottom: 0px;
  }

  @mixin flag-icon($country) {
    .flag-#{"" + $country} {
      background-image: url('https://cdn.lobox.com/assets/images/flags/#{"" + $country}.svg');
    }
  }

  $countries: (
    'af',
    'al',
    'dz',
    'ad',
    'ao',
    'ag',
    'ar',
    'am',
    'aw',
    'au',
    'at',
    'az',
    'bs',
    'bh',
    'bd',
    'bb',
    'by',
    'be',
    'bz',
    'bj',
    'bt',
    'bo',
    'ba',
    'bw',
    'br',
    'io',
    'bn',
    'bg',
    'bf',
    'bi',
    'kh',
    'cm',
    'ca',
    'cv',
    'bq',
    'cf',
    'td',
    'cl',
    'cn',
    'co',
    'km',
    'cd',
    'cg',
    'ck',
    'cr',
    'ci',
    'hr',
    'cu',
    'cw',
    'cy',
    'cz',
    'dk',
    'dj',
    'dm',
    'do',
    'ec',
    'eg',
    'sv',
    'gq',
    'er',
    'ee',
    'sz',
    'et',
    'fk',
    'fo',
    'fj',
    'fi',
    'fr',
    'gf',
    'pf',
    'ga',
    'gm',
    'ge',
    'de',
    'gh',
    'gi',
    'gr',
    'gl',
    'gd',
    'gp',
    'gu',
    'gt',
    'gg',
    'gn',
    'gw',
    'gy',
    'ht',
    'hn',
    'hk',
    'hu',
    'is',
    'in',
    'id',
    'ir',
    'iq',
    'ie',
    'im',
    'il',
    'it',
    'jm',
    'jp',
    'je',
    'jo',
    'kz',
    'ke',
    'ki',
    'kw',
    'kg',
    'la',
    'lv',
    'lb',
    'ls',
    'lr',
    'ly',
    'li',
    'lt',
    'lu',
    'mo',
    'mg',
    'mw',
    'my',
    'mv',
    'ml',
    'mt',
    'mh',
    'mq',
    'mr',
    'mu',
    'yt',
    'mx',
    'fm',
    'md',
    'mc',
    'mn',
    'me',
    'ms',
    'ma',
    'mz',
    'mm',
    'na',
    'nr',
    'np',
    'nl',
    'nc',
    'nz',
    'ni',
    'ne',
    'ng',
    'nu',
    'nf',
    'kp',
    'mk',
    'mp',
    'no',
    'om',
    'pk',
    'pw',
    'ps',
    'pa',
    'pg',
    'py',
    'pe',
    'ph',
    'pn',
    'pl',
    'pt',
    'pr',
    'qa',
    're',
    'ro',
    'ru',
    'rw',
    'bl',
    'sh',
    'kn',
    'lc',
    'mf',
    'pm',
    'vc',
    'ws',
    'sm',
    'st',
    'sa',
    'sn',
    'rs',
    'sc',
    'sl',
    'sg',
    'sx',
    'sk',
    'si',
    'sb',
    'so',
    'za',
    'gs',
    'kr',
    'ss',
    'es',
    'lk',
    'sd',
    'sr',
    'sj',
    'se',
    'ch',
    'sy',
    'tw',
    'tj',
    'tz',
    'th',
    'tl',
    'tg',
    'tk',
    'to',
    'tt',
    'tn',
    'tr',
    'tm',
    'tc',
    'tv',
    'ug',
    'ua',
    'ae',
    'gb',
    'us',
    'um',
    'uy',
    'uz',
    'vu',
    'va',
    've',
    'vn',
    'vg',
    'vi',
    'wf',
    'eh',
    'ye',
    'zm',
    'zw'
  ); //  the country codes  supported
  @each $country in $countries {
    @include flag-icon($country);
  }
}
