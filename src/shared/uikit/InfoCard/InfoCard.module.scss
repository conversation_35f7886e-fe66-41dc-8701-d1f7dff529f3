@import '/src/shared/theme/theme.scss';
@layer uikit2 {
  .wrapper {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: variables(gutter) * 0.5;
    background-color: colors(warning_10);
    border-radius: 8px;
  }
  .leftWrapper {
    flex-direction: row;
    align-items: center;
  }
  .leftIcon {
    margin-right: variables(gutter) * 0.5;
  }
  .label {
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    color: colors(smoke_coal);
  }
  .rightIcon {
    cursor: pointer;
    height: 18px;
  }

  @media (min-width: breakpoints(tablet)) {
    .wrapper {
    }
  }
}
