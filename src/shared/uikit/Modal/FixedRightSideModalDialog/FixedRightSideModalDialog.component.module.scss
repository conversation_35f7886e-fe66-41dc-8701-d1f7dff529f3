@import '/src/shared/theme/theme.scss';

@media (min-width: breakpoints(tablet)) {
  .backdrop {
    -webkit-backdrop-filter: unset;
    top: variables(mainHeaderDesktopHeight);
    left: variables(sidePanelWidth);
    background-color: unset;
    backdrop-filter: unset;
    height: auto;
    width: 0;
  }

  .wideBackdrop {
    background-color: colors(modalBlur);
    position: fixed;
    bottom: 0;
    top: variables(mainHeaderDesktopHeight);
    left: variables(sidePanelWidth);
    right: 0;
    width: unset;
    height: unset;
    backdrop-filter: blur(1px);
  }
  .fullBackdrop {
    left: 0 !important;
  }
  .wrapper {
    width: variables(rightSideModalMaxWidth) !important;
  }

  .content {
    position: fixed;
    top: 72px;
    right: 0;
    width: variables(rightSideModalMaxWidth) !important;
    height: calc(100% - 72px);
    margin: 0;
    border-radius: 0;
    bottom: 0;
    overflow: hidden !important;
  }
  .wrapperWide {
    width: variables(wideRightSideModalMaxWidth) !important;
    max-width: variables(wideRightSideModalMaxWidth) !important;
  }
  .dialogWide {
    max-width: variables(wideRightSideModalMaxWidth) !important;
  }
  .modalRoot {
    border-radius: 0;
    height: 100%;
    background: colors(background);
  }
  @media (min-width: breakpoints(tablet)) {
    .modalClassName {
      top: 0;
    }
    .modalRoot {
      border-top: unset;
      border-bottom: unset;
      border-right: unset;
      border-left: 1px solid colors(techGray_20);
      border-radius: 0;
      height: 100%;
    }
  }
  .contentWide {
    max-width: variables(wideRightSideModalMaxWidth) !important;
  }
  .contentHuge {
    max-width: unset !important;
    width: 100%
  }
  .doubleSizeWrapper {
    width: variables(doubleRightSideModalMaxWidth) !important;
    max-width: variables(doubleRightSideModalMaxWidth) !important;
  }
}
