import React, { useRef } from 'react';
import useMedia from '@shared/uikit/utils/useMedia';
import cnj from 'shared/uikit/utils/cnj';
import ModalDialog from '../ModalDialog/Modal.Dialog';
import classes from './FixedRightSideModalDialog.component.module.scss';
import type { ModalDialogProps } from '../ModalDialog/Modal.Dialog';

export type FixedRightSideModalDialogProps = Partial<ModalDialogProps> & {
  onClickOutside?: (e: MouseEvent) => void;
  wide?: boolean;
  doubleColumn?: boolean;
  isOpenAnimation?: boolean;
  fullBackdrop?: boolean;
  isCustom?: boolean;
  customConfirmationProps?: any;
  isNarrow?: boolean;
};

const FixedRightSideModalDialog = ({
  children,
  onClickOutside,
  wide,
  doubleColumn,
  fullBackdrop,
  isCustom,
  customConfirmationProps,
  isOpenAnimation: propIsOpenAnimation,
  modalDialogClassName,
  modalClassName,
  contentClassName,
  isNarrow,
  ...rest
}: FixedRightSideModalDialogProps) => {
  const modalBodyRef = useRef<HTMLDivElement>(null);
  const { isTabletAndLess } = useMedia();
  const isOpenAnimation = isTabletAndLess ? false : propIsOpenAnimation;

  return (
    <ModalDialog
      isOpen
      {...rest}
      backdropClassName={cnj(
        classes.backdrop,
        classes.wideBackdrop,
        rest.backdropClassName,
        fullBackdrop && classes.fullBackdrop
      )}
      modalClassName={cnj(classes.modalClassName, modalClassName)}
      modalDialogClassName={cnj(
        classes.content,
        wide
          ? doubleColumn
            ? classes.doubleSizeWrapper
            : classes.wrapperWide
          : undefined,
        classes.blureBg,
        modalDialogClassName
      )}
      contentClassName={cnj(classes.modalRoot, contentClassName)}
      isOpenAnimation={isOpenAnimation}
      modalWrapperProps={{ onBackdropClick: onClickOutside }}
      confirmVariant="wideRightSideModal"
      customConfirmationProps={customConfirmationProps}
      isNarrow={isNarrow}
    >
      <div ref={modalBodyRef} className="contents">
        {children}
      </div>
    </ModalDialog>
  );
};

export default FixedRightSideModalDialog;
