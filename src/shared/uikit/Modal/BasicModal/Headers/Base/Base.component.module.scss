@import '/src/shared/theme/theme.scss';

@layer molecule {
  .wrapper {
    z-index: 10;
    position: sticky;
    top: 0;
    min-height: variables(headerMobileHeight);
    background: colors(background);
  }

  .modalHeader {
    flex-shrink: 0;
    flex-direction: row;
    align-items: center;
    height: variables(headerMobileHeight);
    min-height: variables(headerMobileHeight);
    padding: variables(gutter);
    width: 100%;
    border-bottom: 1px solid colors(techGray_10);
  }

  .backBtn {
    margin-right: variables(xLargeGutter) * 0.5;
    display: flex;
    padding: 6px;
  }
  .headerContent {
    flex: 1;
    flex-direction: row;
    align-items: center;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .closeBtn {
    display: none;
    align-items: center;
    margin-left: auto;
    position: absolute;
    right: variables(gutter);
  }
  .divider {
    border-bottom: 1px solid colors(techGray_10);
  }
  @media (min-width: breakpoints(tablet)) {
    .wrapper {
      min-height: variables(headerDesktopHeight);
    }
    .modalHeader {
      padding: variables(largeGutter);
      height: variables(headerDesktopHeight);
      min-height: variables(headerDesktopHeight);
    }
    .backBtn {
      display: flex;
      padding: 0;
    }
    .backBtnHidden {
      display: none;
    }
    .closeBtn {
      display: flex;
    }
  }
}
