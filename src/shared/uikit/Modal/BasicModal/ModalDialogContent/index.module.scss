@import '/src/shared/theme/theme.scss';
@layer molecule {
  .gradiant {
    background: colors(modalHeaderBackground);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 58px;
  }
  .modal {
    display: flex;
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
    background: colors(background);
  }
  .modalhidden {
    display: none;
  }
  .modalDialog {
    margin-top: 0;
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0;
    pointer-events: none;
    & * {
      -webkit-font-smoothing: antialiased !important;
    }
  }
  .modalContent {
    background: colors(background);
    position: relative;
    width: 100%;
    pointer-events: auto;
    background-clip: padding-box;
    border: unset;
    border-radius: 0;
    outline: 0;
    max-width: 100%;
    margin: auto;
    height: 100%;
  }

  .fillWrapper {
    height: 100%;
    width: 100%;
  }
  @media (min-width: breakpoints(tablet)) {
    .gradiant {
      top: 0;
      border-radius: 12px;
    }
    .modal {
      overflow: auto;
      background: unset;
    }
    .modalDialog {
      max-height: calc(100vh - 72px);
      width: auto;
      margin: auto 0.5rem;
      height: 100%;
    }
    .modalContent {
      border: 1px solid colors(borderSecond);
      border-radius: 12px;
      max-width: 650px;
      overflow: auto;
      height: unset;
    }
  }
}
