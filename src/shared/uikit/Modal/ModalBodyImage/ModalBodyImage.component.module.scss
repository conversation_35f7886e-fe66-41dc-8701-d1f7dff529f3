@import '/src/shared/theme/theme.scss';;

@layer molecule {
  .gradientImageContainer {
    justify-content: center;
    align-items: center;
    & svg {
      width: auto;
      height: 170px;
    }
    & img {
      width: auto;
      height: auto;
    }
  }
  .divider {
    margin-bottom: variables(gutter);
  }
  @media (min-width: breakpoints(tablet)) {
    .gradientImageContainer {
    }
    .divider {
      margin-bottom: variables(largeGutter);
    }
  }
}
