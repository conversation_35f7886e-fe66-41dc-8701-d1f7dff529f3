@import '/src/shared/theme/theme.scss';;

$PB: $SafeArea + variables(gutter);

@layer uikit {
  .modalFooter {
    flex-shrink: 0;
    padding: variables(gutter);
    z-index: 3;
    padding-bottom: calc(#{variables(safeAreaInsetBottom)} + variables(gutter));
    border-top: 1px solid colors(techGray_10);
    background-color: colors(background);
    position: sticky;
    bottom: 0;
  }
  @media (min-width: breakpoints(tablet)) {
    .modalFooter {
      padding: variables(largeGutter);
    }
  }
}
