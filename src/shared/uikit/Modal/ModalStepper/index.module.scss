@import '/src/shared/theme/theme.scss';

@layer molecule {
  .root {
    flex-direction: row;
    gap: variables(gutter) * 0.5;
    align-items: center;
    padding: variables(gutter) * 0.5 variables(gutter);
    background-color: colors(gray_5);
    height: 48px;
    .rightSection {
      margin-left: auto;
      flex-direction: row;
      align-items: center;
      gap: variables(gutter) * 0.25;
    }
    @media (min-width: breakpoints(tablet)) {
      height: 56px;
      padding: variables(xLarge<PERSON>utter) * 0.5 variables(largeGutter);
    }
  }
}
