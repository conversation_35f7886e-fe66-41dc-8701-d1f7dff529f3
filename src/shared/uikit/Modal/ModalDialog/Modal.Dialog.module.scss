@import '/src/shared/theme/theme.scss';

@layer molecule {
  .backdrop {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    background: colors(modalBlur);
    z-index: 1050;
    width: 100vw;
    height: 100vh;
    backdrop-filter: blur(1px);
  }
  .hidBackdrop {
    background: colors(transparent);
    -webkit-backdrop-filter: unset;
    backdrop-filter: unset;
  }
  .hidden {
    display: none;
  }
}
