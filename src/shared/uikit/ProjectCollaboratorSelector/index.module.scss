@import '/src/shared/theme/theme.scss';

@layer organism {
  .root {
    gap: 8px;
    .dropDown {
      flex: 1;
    }
  }

  .avatarBox {
    padding: 0;
    background-color: transparent;
    pointer-events: all;
    &:hover {
      background-color: transparent;
    }
  }

  .rightIconClassName {
    pointer-events: all;
  }

  .avatarBoxWrapper {
    padding: 0;
    justify-content: center;
  }
  .inputWrapClassName {
    padding: 0 variables(gutter);
  }
  .inputStyle {
    padding-left: 0;
  }
  .labelClassName {
    padding-left: variables(xLargeGutter);
  }
  .tagName {
    color: colors(secondaryDisabledText);
  }

  .addButton {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: variables(gutter);
    border: 1px dashed colors(techGray_20);
    border-radius: variables(gutter) * 0.25;
    flex-direction: row;
    gap: variables(gutter) * 0.5;
  }

  .collaboratorsRoot {
    gap: variables(gutter);
  }

  .moreOptions {
    margin-left: auto;
    flex-direction: row;
    align-items: center;
    gap: variables(gutter) * 0.5;
    .isActive {
      padding: variables(gutter) * 0.25 variables(gutter) * 0.5;
      background-color: colors(brand_10);
      border-radius: variables(gutter) * 0.25;
    }
  }
}
