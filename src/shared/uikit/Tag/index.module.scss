@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .tagContainer {
    flex-direction: row;
    align-items: center;
    background-color: colors(background8);
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 4px;
    cursor: pointer;
    margin-top: 4px;
    &:hover {
      background-color: colors(tagHover);
      & span {
        color: colors(background7);
      }
    }
  }
  .tagContainerBadge {
    padding: 2px 8px;
    margin-right: 0;
  }
  .disabledHover {
    &:hover {
      background-color: inherit;
      & span {
        color: inherit;
      }
    }
  }
  .close {
    margin-left: 4px;
  }

  .tag_inner {
    color: colors(background7);
    flex: 1;
    word-break: break-all;
    line-height: 22px;
  }
  .tag_innerBadge {
    line-height: 18px;
  }

  .selected {
    background: colors(smoke_brand);
    & span {
      color: colors(background);
    }
    &:hover {
      background-color: colors(smoke_trench);
      & span {
        color: colors(background);
      }
    }
  }
}
