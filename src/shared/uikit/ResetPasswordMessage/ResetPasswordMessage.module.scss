@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .icon {
    margin-right: variables(largeGutter);
    color: colors(thirdText);
    padding: variables(largeGutter) * 0.5;
    background: colors(background3);
    border-radius: 100px;
    align-self: flex-start;
    font-size: 20px;
  }

  .form {
    flex-direction: row;
  }
  .input {
    flex: 1;
    margin-right: variables(largeGutter);
  }
  .submitButton {
    align-self: center;
  }
  .hintWrap {
    background-color: colors(background6);
    flex-direction: row;
    padding: variables(largeGutter);
    border-radius: 4px;
    font-family: fonts(regular);
  }
  .enterTheCode {
    margin-bottom: variables(largeGutter) * 0.5;
  }
  .letUsKnow {
    margin: variables(largeGutter) 0;
  }
  .emailIcon {
    padding: variables(largeGutter);
  }
  .resendWrap {
    margin: variables(largeGutter) 0;
    font-family: fonts(regular);
  }
  .action {
    cursor: pointer;
  }
  @media (min-width: breakpoints(tablet)) {
    .resendWrap {
      flex-direction: row;
    }
    .clickToResend {
      margin-left: 4px;
    }
  }
}
