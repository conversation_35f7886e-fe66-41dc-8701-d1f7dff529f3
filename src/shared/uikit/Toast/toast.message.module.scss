@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .textWrapper {
    word-break: break-word;
    display: inline;
    & > * {
      display: inline;
    }
  }
  .paddingTop {
    padding-top: variables(gutter) * 0.5;
  }
  .messageWrap {
  }

  @media (min-width: breakpoints(tablet)) {
    .textWrapper {
      max-width: 400px;
    }
    .paddingTop {
      padding-top: variables(largeGutter) * 0.5;
    }
  }
}
