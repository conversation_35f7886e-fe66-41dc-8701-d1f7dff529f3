@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .snackBarContainer {
  }
  .snackBarContent {
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
  }
  .toastIcon {
    align-self: center;
    margin-right: variables(gutter) * 0.5;
    align-self: flex-start;
  }
  .actionWrapper {
    margin-left: auto;
  }
  .titleWrapper {
    margin-right: variables(gutter);
  }

  @media (min-width: breakpoints(tablet)) {
    .toastIcon {
      margin-right: variables(largeGutter) * 0.5;
    }
    .titleWrapper {
      margin-right: variables(largeGutter);
    }
  }
}
