@import '/src/shared/theme/theme.scss';

@layer uikit {
  @keyframes rippleAnimation {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.5;
      transform: scale(50);
    }
    100% {
      opacity: 0;
      transform: scale(100);
    }
  }

  .ripple {
    width: 20px;
    height: 20px;
    position: absolute;
    background: colors(borderEighth);
    display: block;
    content: '';
    border-radius: 100%;
    opacity: 1;
    animation: rippleAnimation 1s ease 1 forwards;
  }
  .rippleWrapper {
    overflow: hidden;
    position: relative;
    -ms-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -webkit-user-select: none;
  }
}
