@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .strengthMeter {
    margin-top: 20px;
    position: relative;
    height: 5px;
    background: colors(smoke);
    border-radius: 4px;
    &:before {
      content: '';
      height: inherit;
      background: colors(transparent);
      display: block;
      position: absolute;
      width: calc(25% + 6px);
      z-index: 10;
      left: calc(25% - 3px);
    }
    &:after {
      content: '';
      height: inherit;
      background: colors(transparent);
      display: block;
      position: absolute;
      width: calc(25% + 6px);
      z-index: 10;
      right: calc(25% - 3px);
    }
  }
  .strengthMeterFill {
    background: colors(transparent);
    height: inherit;
    position: absolute;
    width: 0;
    border-radius: inherit;
    transition: width 0.5s ease-in-out;
    &[data-strength='0'] {
      width: 25%;
      background: colors(error);
    }
    &[data-strength='1'] {
      width: 50%;
      background: colors(warning);
    }
    &[data-strength='2'] {
      width: 75%;
      background: colors(lightGreen);
    }
    &[data-strength='3'] {
      width: 100%;
      background: colors(success);
    }
  }
}
