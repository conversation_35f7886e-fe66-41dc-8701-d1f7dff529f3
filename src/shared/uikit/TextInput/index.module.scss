@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .inputRoot {
    font-family: fonts(regular);
    pointer-events: auto;
    opacity: 1;
    user-select: auto;
  }
  .disabled {
    pointer-events: none;
    opacity: 0.5;
    user-select: none;
  }
  .disabledReadOnly {
    pointer-events: none;
    user-select: none;
    .inputWrapReadOnly {
      background-color: colors(darkSecondary_hover);
    }
  }

  .inputWrap {
    position: relative;
    border-radius: 4px;
    background-color: colors(background);
    height: 56px;
    width: 100%;
    justify-content: end;
    border: 1px solid colors(techGray_20);
    transition: border-color 0.3s ease;

    &.hasRightAddon {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &.hasLeftAddon {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &:hover {
      border: 1px solid colors(secondaryDisabledText);
    }
  }
  .inputWrapReadOnly {
    background-color: colors(popOverBg_white);
  }
  .borderColorError {
    border-color: colors(error);
    &:hover {
      border: 1px solid colors(error);
    }
  }
  .borderColorTransparent {
    border-color: colors(transparent);
    &:hover {
      border: 1px solid colors(transparent);
    }
  }
  .borderColorFocused {
    border-color: colors(brand);
    &:hover {
      border-color: colors(brand);
    }
  }
  .leftIcon {
    justify-content: center;
    padding: 0;
  }

  .input {
    font-size: 16px;
    color: colors(smoke_coal);
    background: colors(transparent);
    border-radius: 3px;
    width: 100%;
    padding: 28px variables(gutter) variables(gutter) * 0.5;
    text-overflow: ellipsis;

    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus {
      -webkit-text-fill-color: colors(thirdText);
      -webkit-box-shadow: 0 0 0 1000px colors(darkSecondary_autofillLight) inset;
    }

    &::selection {
      color: colors(white);
      background: colors(brand);
    }
  }
  .suggestedCompletionText {
    position: absolute;
    bottom: variables(gutter) * 0.5;
    top: 27px;
    left: variables(gutter);
    pointer-events: none;
    font-size: 16px;
  }
  .suggestedCompletionTextValue {
    color: transparent;
  }
  .suggestedCompletionTextSuggestion {
    color: colors(techGray_20);
  }

  .inputWithLeftIcon {
    padding: 28px variables(gutter) * 0.5 variables(gutter) * 0.5;
  }

  .wrapBtn {
    height: auto;
    padding: 0;
    text-align: left;
    justify-content: flex-start;
    align-items: stretch;
    flex-direction: column;
    &.hasAddon {
      flex-direction: row;
    }
  }

  .bell {
    color: colors(error);
    font-size: 20px;
  }

  .label {
    position: absolute;
    top: variables(gutter);
    left: 0;
    right: auto;
    padding-left: variables(gutter);
    padding-top: 2px;
    z-index: 1;
    font-size: 15px;
    line-height: 22px;
  }

  .labelWithLeftIcon {
    left: 44px;
  }

  .helperText {
    margin-left: variables(gutter);
    margin-top: 4px;
    overflow: hidden;
  }

  .errorText {
    color: colors(error);
  }

  .activeInput {
    border: 1px solid colors(brand);
  }

  .errorBoundary {
    border: 1px solid colors(error);

    &:hover {
      border: 1px solid colors(error);
    }
  }

  .rightIcon {
    position: absolute;
    right: 0;
    bottom: 0;
    top: 0;
    justify-content: center;
    align-items: center;
    margin: auto 0;
    z-index: 1;
    padding-right: variables(gutter);
    padding-left: 8px;
    background-color: inherit;
  }

  .cursorDefault {
    cursor: default;
  }

  @keyframes slideUp {
    from {
      font-size: 15px;
      top: variables(gutter);
    }
    to {
      font-size: 12px;
      top: variables(gutter) * 0.125;
    }
  }

  .active {
    animation-name: slideUp;
    animation-duration: 100ms;
    animation-fill-mode: forwards;
    animation-delay: 10ms;
  }

  .defaultActive {
    font-size: 12px;
    top: variables(gutter) * 0.125;
  }

  .helperWrap {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .maxLength {
    margin-left: auto;
    margin-top: 4px;
  }
  .row {
    flex-direction: row;
    gap: variables(gutter) * 0.5;
  }
}
