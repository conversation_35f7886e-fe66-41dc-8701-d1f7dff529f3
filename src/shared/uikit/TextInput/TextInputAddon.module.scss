@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .wrapper {
    align-items: center;
    padding-left: variables(gutter);
    padding-right: variables(gutter) * 0.5;
    gap: variables(gutter) * 0.5;
    height: 100%;
    background-color: colors(popOverBg_white);
    pointer-events: none;
    user-select: none;
    border: 1px solid colors(techGray_20);
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .left {
    padding-left: variables(gutter);
    padding-right: variables(gutter) * 0.5;
    border-right: 0;
  }
  
  .right {
    flex-direction: row-reverse;
    padding-right: variables(gutter);
    padding-left: variables(gutter) * 0.5;
    border-left: 0;
  }
}

