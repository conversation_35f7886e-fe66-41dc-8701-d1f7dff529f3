@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .previewWrapper {
    align-items: center;
  }

  .cVWrapper {
    height: 64px;
    width: 64px;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    border: 1px solid colors(techGray_20);
    padding: 4px 12px;
    //margin-bottom: variables(gutter) / 2;
  }

  .iconWrapper {
    justify-content: center;
    align-items: center;
    margin-right: variables(xLargeGutter) * 0.5;
  }

  @media (min-width: breakpoints(tablet)) {
    .iconWrapper {
      margin-right: variables(largeGutter);
    }
  }
}
