@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .iconWrapper {
    height: 136px;
    width: 104px;
    background: colors(brand_10);
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    margin: variables(gutter) 0;
  }
  .icon {
    font-weight: 900;
    justify-content: center;
    color: colors(brand);
  }
  .fileContainer {
    justify-content: center;
    align-items: center;
    //padding: variables(gutter) / 2 variables(largeGutter);
    background: colors(background);
    border-radius: variables(gutter) * 0.25;
    align-self: stretch;
    height: 100%;
    position: relative;
  }

  .leftUploading {
    width: 100%;
  }
  .filedContentClassName {
    width: 100%;
  }
  .uploading {
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .uploadingContent {
    flex: 1;
    width: 100%;
    gap: variables(gutter) * 0.5;
    align-items: center;
  }
  .progressbarWrapper {
    margin-top: variables(largeGutter) * 0.5;
    flex: 1;
  }
  .progressClassName {
    background-color: colors(sky);
  }
  .progressRoot {
    background-color: colors(borderFifth);
    margin-top: 12px;
  }
  @media (min-width: breakpoints(tablet)) {
    .filedContentClassName {
    }
  }
}
