@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .resumeContainer {
    height: 100%;
  }
  .container {
    height: 100%;
  }
  .dropzone {
    height: 100%;
  }
  .title {
    margin-bottom: variables(gutter) * 0.5;
  }
  .iconWrapper {
    height: 109px;
    justify-content: center;
    align-items: center;
    margin-bottom: variables(gutter);
  }
  .cVWrapper {
    height: 90px;
    width: 90px;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    border: 1px solid colors(techGray_20);
    margin-bottom: variables(gutter) * 0.5;
  }
  .fileTitleHeader {
    width: 100%;
    max-width: unset;
    overflow: hidden;
    text-align: center;
  }
  .fileTitle {
    margin-bottom: variables(gutter) * 0.5;
  }
  .icon {
    font-weight: 900;
    justify-content: center;
    color: colors(brand);
    height: 63px;
  }
  .info {
    margin-top: 32px;
  }
  .fileContainer {
    justify-content: center;
    align-items: center;
    padding: variables(gutter) * 0.5 variables(largeGutter);
    background: colors(background);
    border-radius: variables(gutter) * 0.25;
    align-self: stretch;
    height: 100%;
    position: relative;
  }
  .errorBorder {
    border: 1px dashed colors(error);
  }
  .addBtn {
    height: 100%;
    justify-content: center;
    align-items: center;
  }
  .isDragEntered {
    //border: 1px solid colors(brand);
    background: colors(hoverPrimary);
    border-radius: 12px;
  }
  .removeIconDone {
    justify-content: center;
    align-items: center;
    margin: 0 variables(gutter) * 0.5;
  }
  .dateLabel {
    margin-bottom: 12px;
  }

  .footer {
    position: absolute;
    bottom: variables(largeGutter);
  }
  .footerLabel {
    margin-bottom: variables(gutter) * 0.5;
    text-decoration: underline;
    text-align: center;
  }
  .errorText {
    margin-top: 4px;
    overflow: hidden;
  }
  .baseButton {
    height: 100%;
  }
  @media (min-width: breakpoints(tablet)) {
    .iconWrapper {
      height: 78px;
      margin-bottom: 12px;
    }
    .resumeContainer {
      &_hover {
        &:hover {
          background: colors(hoverPrimary);
        }
      }
    }
    .info {
      margin-top: 32px;
    }
  }
}
