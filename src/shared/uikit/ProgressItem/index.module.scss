@import '/src/shared/theme/theme.scss';
@layer uikit {
  $IMAGE_WIDTH: 30px;
  $HEIGHT_RATIO: 3*0.25;

  .actionButton {
    visibility: visible;
  }

  .titleWrapper {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: variables(gutter) * 0.5;
    gap: variables(gutter) * 0.5;
  }

  .image {
    margin-right: variables(gutter) * 0.5;
    width: $IMAGE_WIDTH !important;
    height: calc($IMAGE_WIDTH * $HEIGHT_RATIO) !important;
    min-width: $IMAGE_WIDTH;
    min-height: calc($IMAGE_WIDTH * $HEIGHT_RATIO);
    object-fit: cover;
    border-radius: 4px;
  }

  .actionWrapper {
    margin-left: auto;
  }

  .subtitle {
    border-left: 1px solid colors(techGray_20);
    padding-left: variables(gutter) * 0.5;
    margin-left: variables(gutter) * 0.5;
  }

  .progressItemTitle {
    padding: 3px 0;
    margin-right: auto;
  }

  @media (min-width: breakpoints(tablet)) {

    .actionButton {
      visibility: hidden;
    }
    .alwaysVisibleActionButton {
      visibility: visible!important
    }

    .progressItemRoot {
      &:hover {
        .actionButton {
          visibility: visible;
        }
      }
    }
  }
}
