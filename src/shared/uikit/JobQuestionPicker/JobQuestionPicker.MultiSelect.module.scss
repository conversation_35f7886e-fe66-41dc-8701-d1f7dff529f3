@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .footer {
    justify-content: space-between;
  }
  .title {
    margin: variables(gutter) * 0.5 0;
  }
  .alert {
    justify-content: center;
    align-items: center;
    height: variables(desktopGutter);
    padding: 0 12px;
    margin-top: variables(gutter) * 0.5;
  }
  .mustHave {
    flex-direction: row-reverse;
    align-self: center;
  }
  .mustHaveCheckbox {
    margin-left: variables(gutter) * 0.5;
  }
  .answer {
    margin-top: variables(gutter) * 0.5;
    margin-bottom: variables(gutter) * 0.5;
  }
  .option {
    margin-bottom: variables(gutter) * 0.5;
  }
  @media (min-width: breakpoints(tablet)) {
    .option {
      margin-bottom: 12px;
    }
    .title {
      margin: 12px 0;
    }
    .answer {
      margin-bottom: 12px;
    }
  }
}
