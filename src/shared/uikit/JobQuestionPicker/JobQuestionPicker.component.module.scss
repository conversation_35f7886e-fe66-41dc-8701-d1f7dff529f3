@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .header {
    justify-content: space-between;
    margin-top: variables(gutter);
  }
  .divider {
    margin-top: variables(gutter) * 0.5;
  }
  .firstHeader {
    justify-content: space-between;
  }
  .dropDownClassName {
    margin-top: variables(gutter) * 0.5;
  }
  .headerActions {
    align-items: center;
  }
  .title {
    margin: 12px 0;
  }
  .addAnotherBtn {
    margin-top: variables(gutter);
  }
  .icon {
    margin: 0 variables(gutter) * 0.5;
    cursor: pointer;
  }
  .isDragging {
    background: colors(background);
  }
  .alert {
    margin-top: variables(gutter);
    justify-content: center;
    align-items: center;
    height: variables(desktopGutter);
  }
  @media (min-width: breakpoints(tablet)) {
    .dragParent {
      left: 0 !important;
    }
    .dropDownClassName {
      margin-top: 12px;
    }
    .addAnotherBtn {
      margin-top: variables(largeGutter);
    }
    .alert {
      margin-top: variables(largeGutter);
    }
  }
}
