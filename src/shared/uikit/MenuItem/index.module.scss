@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .listItemRoot {
    flex-direction: row;
    gap: variables(gutter) * 0.5;
    border-radius: variables(gutter) * 0.25;
    padding: variables(gutter) * 0.5;
    align-items: stretch;
    justify-content: start;
  }
  .titles {
    flex-direction: row;
    flex-grow: 1;
    align-items: center;
    justify-content: space-between;
  }
  .multiLine {
    flex-direction: column;
    gap: variables(gutter) * 0.25;
    justify-content: center;
    flex-grow: 1;
    position: relative;
  }
  .line {
    flex-direction: row;
    align-items: center;
    flex-grow: 1;
  }
  .singleTitle {
    font-weight: 400;
    font-size: 15px;
    line-height: 17px;
  }
  .bigTitle {
    font-weight: 700;
    font-size: 15px;
    color: colors(smoke_coal);
    line-height: 17px;
  }
  .smallTitle {
    font-weight: 400;
    font-size: 12px;
    color: colors(secondaryDisabledText);
    line-height: 14px;
  }
  .secondaryLabel {
    color: colors(secondaryDisabledText);
    font-weight: 400;
    font-size: 12px;
    list-style: none;
    padding-left: variables(largeGutter);
    position: relative;
  }
  .secondaryLabel:before {
    content: '•';
    color: colors(secondaryDisabledText);
    position: absolute;
    left: variables(largeGutter) * 0.5;
    top: 1px;
  }
  .statusLabel {
    line-height: 13px;
    font-weight: 600;
    font-size: 10px;
    margin-left: auto;
  }

  .active {
    background-color: colors(brand);
    .singleTitle,
    .bigTitle,
    .smallTitle {
      color: colors(white);
    }
  }
  .straightRow {
    flex-direction: row;
    flex-grow: 1;
    gap: variables(gutter) * 0.5;
  }
  .revertRow {
    flex-grow: 1;
    flex-direction: row-reverse;
    gap: variables(gutter) * 0.5;
  }
  .action {
    &.top {
      justify-content: start;
    }
    &.center {
      justify-content: center;
    }
  }
  .disabledAction {
    opacity: 0.3;
    pointer-events: none;
  }

  @media (min-width: breakpoints(tablet)) {
    .withHover {
      &:hover {
        background-color: colors(hoverPrimary);
      }
    }
    .active.withHover:hover {
      background-color: colors(trench);
    }
  }
}
