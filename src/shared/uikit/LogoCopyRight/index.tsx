import React from 'react';
import useTheme from '@shared/uikit/utils/useTheme';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from '../Flex';
import cnj from '../utils/cnj';
import classes from './index.module.scss';

interface LogoCopyRightProps {
  className?: string;
  logoGray?: boolean;
}

const LogoCopyRight: React.FC<LogoCopyRightProps> = ({
  className,
  logoGray,
}) => {
  const { t } = useTranslation();
  const { theme, isDark } = useTheme();

  const gray = isDark ? theme.colors.disabledGrayDark : theme.colors.gray;

  return (
    <Flex className={cnj(classes.LogoCopyRightRoot, className)}>
      <svg
        width="50"
        height="14"
        viewBox="0 0 50 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6 11.6311H3.45944C3.41108 11.6311 2.27791 11.6153 2.27791 10.2398V1.13911C2.27791 0.509923 1.76805 0 1.13895 0C0.509851 0 0 0.509923 0 1.13911V10.2398C0 12.623 1.78235 13.909 3.45944 13.909H6C6.6291 13.909 7.13895 13.3991 7.13895 12.7699C7.13895 12.1407 6.6291 11.6311 6 11.6311ZM20.4107 4.65032C21.219 4.11241 22.1894 3.79843 23.2332 3.79843L23.2329 3.79873C26.0495 3.79873 28.3329 6.08242 28.3329 8.89946C28.3329 11.7165 26.0495 14.0002 23.2329 14.0002C22.1751 14.0002 21.1928 13.678 20.3782 13.1267C20.2583 13.6275 19.8093 14.0002 19.2718 14.0002C18.6427 14.0002 18.1328 13.4903 18.1328 12.8611V1.1396C18.1328 0.510411 18.6427 0.000488281 19.2718 0.000488281C19.9009 0.000488281 20.4107 0.510411 20.4107 1.1396V4.65032ZM20.4107 8.89915C20.4107 10.4557 21.6768 11.722 23.2332 11.722C24.7895 11.722 26.0556 10.4557 26.0556 8.89915C26.0556 7.34262 24.7895 6.07633 23.2332 6.07633C21.6768 6.07633 20.4107 7.34262 20.4107 8.89915ZM12.0825 3.67212C9.2658 3.67212 6.98242 5.95581 6.98242 8.77285C6.98242 11.5899 9.2658 13.8736 12.0825 13.8736C14.8991 13.8736 17.1825 11.5899 17.1825 8.77285C17.1825 5.95581 14.8991 3.67212 12.0825 3.67212ZM12.0825 11.5957C10.5261 11.5957 9.26002 10.3294 9.26002 8.77285C9.26002 7.21631 10.5261 5.95003 12.0825 5.95003C13.6388 5.95003 14.9049 7.21631 14.9049 8.77285C14.9049 10.3294 13.6388 11.5957 12.0825 11.5957ZM34.3305 3.67212C31.5139 3.67212 29.2305 5.95581 29.2305 8.77285C29.2305 11.5899 31.5139 13.8736 34.3305 13.8736C37.1472 13.8736 39.4306 11.5899 39.4306 8.77285C39.4306 5.95581 37.1472 3.67212 34.3305 3.67212ZM34.3305 11.5957C32.7742 11.5957 31.5081 10.3294 31.5081 8.77285C31.5081 7.21631 32.7742 5.95003 34.3305 5.95003C35.8868 5.95003 37.153 7.21631 37.153 8.77285C37.153 10.3294 35.8868 11.5957 34.3305 11.5957ZM49.23 5.64502L46.1508 8.79277L49.23 11.9405C49.6699 12.3902 49.662 13.1113 49.2124 13.5512C48.9906 13.7681 48.7116 13.8762 48.416 13.8762C48.1203 13.8762 47.8249 13.7618 47.6019 13.5336L44.5577 10.4214L41.5135 13.5336C41.2905 13.7618 40.9869 13.8762 40.6994 13.8762C40.4119 13.8762 40.1248 13.7681 39.903 13.5512C39.4534 13.1113 39.4455 12.3902 39.8854 11.9405L42.9645 8.79277L39.8854 5.64502C39.4455 5.19534 39.4534 4.47427 39.903 4.03433C40.3526 3.59438 41.0736 3.60229 41.5135 4.05197L44.5577 7.16413L47.6019 4.05197C48.0418 3.60229 48.7628 3.59438 49.2124 4.03433C49.662 4.47427 49.6699 5.19534 49.23 5.64502Z"
          fill={logoGray ? gray : theme.colors.brand}
        />
      </svg>
      <Typography mt={3} className={classes.copyRightText}>
        {`${t('copy_right')} ${new Date().getFullYear()}`}
      </Typography>
    </Flex>
  );
};

export default LogoCopyRight;
