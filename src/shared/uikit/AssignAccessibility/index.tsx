import React from 'react';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import DropdownSelect from 'shared/uikit/AutoComplete/DropdownSelect';
import AvatarCard from 'shared/uikit/AvatarCard';
import Flex from 'shared/uikit/Flex';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import Icon from 'shared/uikit/Icon';
import Tooltip from 'shared/uikit/Tooltip';
import searchEndPoints from 'shared/utils/constants/servicesEndpoints/services/search';
import {
  appPortals,
  appPortalsObject,
  getPortal,
} from 'shared/utils/getAppEnv';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AvatarAsyncAutoComplete from '../AvatarAsyncAutoComplete';
import classes from './index.module.scss';
import type { PaginateResponse } from 'shared/types/response';

type SelectedUserType = {
  id: string;
  title: string;
  image: string;
  username: string;
  isLock: boolean;
};

export interface AssignAccessibility {
  onSelectUser: (obj: SelectedUserType) => void;
}

const allRoles = {
  [appPortalsObject.editor]: [
    'EDITOR_HEAD',
    'EDITOR_EDITOR_MANAGER',
    'EDITOR_EDITOR',
    'EDITOR_MEDIA_MANAGER',
    'EDITOR_MEDIA_DESIGNER',
    'EDITOR_COMMUNITY_MANAGER',
    'EDITOR_COMMUNITY_COORDINATOR',
    'EDITOR_ANALYST',
  ],
  [appPortalsObject.recruiter]: [
    'RECRUITER_HEAD',
    'RECRUITER_RECRUITER_MANAGER',
    'RECRUITER_RECRUITER',
    'RECRUITER_HIRING_MANAGER',
    'RECRUITER_EXPERT_MANAGER',
    'RECRUITER_EXPERT',
    'RECRUITER_ANALYST',
  ],
  [appPortalsObject.campaign]: [
    'CAMPAIGN_HEAD',
    'CAMPAIGN_MARKETING_MANAGER',
    'CAMPAIGN_MARKETING_SPECIALIST',
    'CAMPAIGN_ADS_MANAGER',
    'CAMPAIGN_ADS_SPECIALIST',
    'CAMPAIGN_ANALYST',
  ],
  [appPortalsObject.sales]: [
    'SALES_HEAD',
    'SALES_SALES_MANAGER',
    'SALES_SALES_SPECIALIST',
    'SALES_FINANCE_MANAGER',
    'SALES_FINANCE_SPECIALIST',
    'SALES_ANALYST',
  ],
  [appPortalsObject.service]: [
    'SERVICE_HEAD',
    'SERVICE_SERVICE_MANAGER',
    'SERVICE_SERVICE_SPECIALIST',
    'SERVICE_LEGAL_MANAGER',
    'SERVICE_LEGAL_SPECIALIST',
    'SERVICE_SUPPORT_MANAGER',
    'SERVICE_SUPPORT_SPECIALIST',
    'SERVICE_ANALYST',
  ],
};

const AssignAccessibility = ({
  onSelectUser,
  value,
  onChange,
}: AssignAccessibility): JSX.Element => {
  const { t } = useTranslation();
  const { businessPage } = useGetAppObject();
  const portal = value?.portal?.value || getPortal();
  const roles = allRoles[portal?.toLowerCase()]?.map((i) => ({
    value: i,
    label: t(`${i}_label`),
  }));

  const portals = appPortals
    .filter((portal) => portal === 'recruiter')
    .map((portal) => ({
      label: portal.toUpperCase(),
      value: portal.toUpperCase(),
    }));

  return (
    <Flex className={classes.inputSelectWrapper}>
      <AvatarAsyncAutoComplete
        value={value?.user}
        onChange={(user: any) => {
          if (user?.value) {
            onChange({ ...value, user });
            onSelectUser({
              id: user.value,
              image: user.image,
              title: user.label,
              username: user.username,
              isPrivate: user.isPrivate,
            });
          }
        }}
        name="user"
        label={t('user')}
        textInputProps={{ label: t('user') }}
        url={`${searchEndPoints.suggestObject}?includeUsername=true&userType=PERSON`}
        normalizer={(data: PaginateResponse<any>) =>
          data?.content?.reduce((prev: Array<any>, cur: any) => {
            if (cur.hideIt || cur.id === businessPage?.ownerId) {
              return prev;
            }

            return [
              ...prev,
              {
                label: `${cur.name} ${cur.surname}`,
                value: cur.id,
                image: cur.croppedImageUrl,
                job: cur.occupationName,
                username: `@${cur.username}`,
                isPrivate: !cur.allowPageRoleAssign,
                id: cur.id,
              },
            ];
          }, [])
        }
        rightIcon={
          <Icon color="primaryText" size={15} type="far" name="search" />
        }
        renderItem={({ item }: any) => (
          <AvatarCard
            containerProps={{
              className: classes.itemWrapper,
            }}
            data={{
              title: item.label,
              image: item.image,
              subTitle: item.username,
            }}
            avatarProps={{ name: item.label }}
            action={
              item?.isLock && (
                <Tooltip
                  placement="left"
                  trigger={<Icon name="lock" type="fal" size={18} />}
                >
                  {t('private')}
                </Tooltip>
              )
            }
          />
        )}
      />

      <DropdownSelect
        label={t('portal')}
        options={portals}
        onChange={(portal) => onChange({ ...value, role: undefined, portal })}
        value={value?.portal}
      />
      <DropdownSelect
        optionsVariant="modal"
        label={t('role')}
        options={roles}
        onChange={(role) => onChange({ ...value, role })}
        value={value?.role}
        disabled={!value?.portal?.value}
        disabledReadOnly={!value?.portal?.value}
      />
      <SubmitButton className={classes.addBtn} label={t('add')} />
    </Flex>
  );
};

export default React.memo(AssignAccessibility);
