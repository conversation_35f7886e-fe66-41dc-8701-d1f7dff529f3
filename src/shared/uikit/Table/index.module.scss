@import '/src/shared/theme/theme.scss';

@layer uikit {
  .tableWrapper {
  }
  .table {
    width: 100%;
    table-layout: fixed;
    & thead > tr {
      background-color: colors(gray_5);
      border-bottom: 1px solid colors(techGray_10);

      & > th {
        padding: variables(largeGutter) * 0.5 0;
      }
    }
    & th,
    td {
      text-align: left;
      vertical-align: middle;
    }

    & td {
      padding: variables(largeGutter) * 0.5 0;
    }
  }
  .odd {
    background-color: colors(darkSecondary_hover);
  }
  .even {
    background-color: colors(background);
  }
}
