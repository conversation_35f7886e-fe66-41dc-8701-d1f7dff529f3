@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .wrapper {
    width: 100vw;
    height: 100%;
    overflow: hidden;
  }
  .content {
    width: 100%;
    height: 100%;
    webkit-flex: 0;
    flex-shrink: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
  .modalRoot {
    width: 100%;
    height: 100%;
    min-height: 100%;
    background: colors(background);
    padding-bottom: variables(safeAreaInsetBottom);
  }

  @media (min-width: breakpoints(tablet)) {
    .wrapper {
      overflow: auto;
    }
    .content {
      height: auto;
      width: 100%;
      max-width: 650px;
      margin-top: 100px;
      margin-bottom: 100px;
    }
    .modalRoot {
      width: 100%;
      max-width: 650px;
      border-radius: 12px;
      height: auto;
      min-height: auto;
      border: 1px solid colors(borderSecond);
    }
  }
}
