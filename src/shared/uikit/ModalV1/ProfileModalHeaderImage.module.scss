@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .modalImgContainer {
    background: colors(modalHeaderBackground);
    min-height: 200px;
    max-height: 200px;
    justify-content: flex-end;
    padding-top: 30px;
  }
  .isRightSide {
    min-height: 230px;
    max-height: 230px;
    background: unset;
  }
  .modalImgWrap {
    align-self: center;
    justify-content: flex-end;
    height: 170px;
    width: auto;
  }
  .divider {
    margin-bottom: variables(gutter);
  }

  @media (min-width: breakpoints(tablet)) {
    .modalImgContainer {
      border-radius: 12px;
      min-height: 250px;
      max-height: 250px;
    }
    .isRightSide {
      min-height: 230px;
      max-height: 230px;
    }
    .modalImgWrap {
      height: 170px;
      width: auto;
    }
    .divider {
      margin: 0 variables(largeGutter) variables(largeGutter)
        variables(largeGutter);
      width: auto;
    }
  }
}
