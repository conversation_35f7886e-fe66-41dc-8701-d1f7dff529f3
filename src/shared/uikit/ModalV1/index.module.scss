@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .backdrop {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: colors(modalBlur);
    z-index: 699;
  }
  .wrapper {
    position: fixed !important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1051;
    outline: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: colors(background);
  }
  .content {
    z-index: 100;
    position: relative;
    margin: auto;
  }

  @media (min-width: breakpoints(tablet)) {
    .wrapper {
      background-color: unset;
    }
  }
}
