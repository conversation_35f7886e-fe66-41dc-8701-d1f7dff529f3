@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .submitWrap {
    padding: variables(gutter);
    position: sticky;
    flex-direction: row;
    bottom: 0;
    background: colors(background);
    align-items: center;
    justify-content: center;
    border-top: 1px solid colors(techGray_10);
    z-index: 1;
  }
  .submitButton {
    flex: 1;
  }
  .deleteButton {
    margin-right: 5px;
    flex: 1;
  }
  .marginLeft {
    margin-left: 5px;
  }
  .deleteLabelStyle {
    white-space: nowrap;
    color: colors(primaryText);
    line-height: 17px;
  }
  .formRoot {
    overflow-y: auto;
    height: 100%;
  }
  .formWrap {
    padding: 0 variables(gutter) variables(gutter);
    flex: 1;
    background: colors(background);
  }
  .requiredHint {
    align-self: flex-start;
    margin-top: variables(gutter);
  }

  @media (min-width: breakpoints(tablet)) {
    .formRoot {
      height: auto;
      padding-bottom: 0;
      overflow-y: unset;
    }
    .formWrap {
      flex: inherit;
      padding: 0 variables(largeGutter);
    }
    .submitWrap {
      position: relative;
      box-shadow: none;
      height: auto;
      border-radius: 0 0 12px 12px;
      padding: variables(largeGutter);
      justify-content: flex-end;
      z-index: unset;
      border: unset;
    }
    .submitButton {
      flex: unset;
    }
    .deleteButton {
      flex: unset;
      margin-right: variables(largeGutter) * 0.5;
    }
    .marginLeft {
      margin-left: 0;
    }
    .requiredHint {
      margin-top: variables(largeGutter);
    }
    .notImage {
      padding-top: variables(headerDesktopHeight);
    }
  }
}
