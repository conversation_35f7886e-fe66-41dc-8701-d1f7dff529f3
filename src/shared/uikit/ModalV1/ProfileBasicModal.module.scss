@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .backBtn {
    margin: 0 variables(gutter);
    //background: colors(transparent);
  }
  .actionLabelStyle {
    display: none;
  }
  .titleWrap {
    flex-direction: row;
    align-items: center;
  }
  .title {
    display: flex;
  }
  .displayNone {
    display: none;
  }

  .modalHeader {
    z-index: 999;
    flex-direction: row;
    align-items: center;
    height: variables(headerMobileHeight);
    min-height: variables(headerMobileHeight);
    padding: 0 variables(gutter);
    position: sticky;
    top: 0;
  }
  .modalHeaderBorderBottom {
    border-bottom: 1px solid colors(techGray_10);
  }
  .closeBtn {
    display: none;
  }
  .searchIcon {
    display: flex;
    flex: 1;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 10px 20px 10px 0;
  }

  @media (min-width: breakpoints(tablet)) {
    .searchIcon {
      display: none;
    }
    .wrap {
      height: auto;
    }
    .verticalLine {
      width: 1px;
      height: 20px;
      background: colors(techGray_20);
      margin-left: variables(gutter);
    }
    .modalHeader {
      border-bottom: unset;
      background: colors(transparent);
      position: sticky;
      left: 0;
      right: 0;
      top: 0;
      padding: 0 variables(largeGutter);
      height: variables(headerDesktopHeight);
      min-height: variables(headerDesktopHeight);
    }
    .modalHeaderBorderBottom {
      border-bottom: 1px solid colors(techGray_10);
    }
    .closeBtn {
      display: flex;
      align-items: center;
      margin: 0 0 0 auto;
    }
    .actionLabelStyle {
      display: flex;
      color: colors(eighthText);
      font-size: 12px;
      line-height: 18px;
      padding-right: variables(gutter) * 0.5;
    }
    .contentWrap {
      margin: 0 variables(largeGutter);
      padding: variables(largeGutter) 0;
    }
    .backBtn {
      margin: 0 variables(largeGutter) 0 0;
    }
  }
}
