@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .skeletonSearchItemRoot {
    flex-direction: row;
    padding: variables(gutter) * 0.5;
  }
  .avatarWrapper {
    margin-right: variables(xLargeGutter) * 0.5;
  }
  .body {
    flex: 1;
  }
  .skeleton {
    &__img {
      width: 40px;
      height: 40px;
      min-height: 40px;
      min-width: 40px;
      border-radius: 99px;
    }
    &__1 {
      width: 100%;
      border-radius: 4px;
      height: 20px;
    }
    &__2 {
      width: 100%;
      height: 16px;
      border-radius: 4px;
      margin-top: 4px;
    }
  }
}
