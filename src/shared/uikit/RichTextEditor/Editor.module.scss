@import '/src/shared/theme/theme.scss';

@layer uikit {
  .tooltip {
    position: absolute;
  }

  .toolbar {
    border: 1px solid colors(techGray_10) !important;
    border-radius: 4px;

    :global {
      & .ql-stroke {
        stroke: colors(graphene);
      }

      & .ql-formats {
        border-right: 1px solid colors(techGray_10);
        padding: 0 variables(gutter) * 0.25;
        margin-right: 0 !important;

        &:first-child {
          padding-left: 0 !important;
        }

        & .ql-header path {
          fill: colors(graphene) !important;
        }

        & .ql-header.ql-active path {
          fill: colors(brand) !important;
        }

        & .ql-header:hover path {
          fill: colors(brand) !important;
        }
      }
    }
  }

  .quillEditorContainerLight {
    :global {
      & .ql-editor > p,
      li,
      span {
        color: colors(coal) !important;
        font-size: 16px;
      }

      & .ql-editor > h1 {
        color: colors(graphene) !important;
        font-size: 20px;
        font-weight: bold;
        padding-bottom: variables(largeGutter);
      }
    }
  }

  .quillEditorContainerDark {
    :global {
      & .ql-editor > p,
      li,
      span {
        color: colors(disabledGray);
        font-size: 16px;
      }

      & .ql-editor > h1 {
        color: colors(smoke);
        font-size: 20px;
        font-weight: bold;
        padding-top: variables(largeGutter);
        padding-bottom: variables(largeGutter);
      }
    }
  }

  .quillEditorContainer {
    @extend .quillEditorContainerOpen;
  }

  .showPlusOnHover {
    @extend .quillEditorContainerOpen;

    & > .editor-options-container {
      opacity: 0;
    }
    &:hover > .editor-options-container {
      opacity: 1;
    }
  }

  .quillEditorContainerOpen {
    position: relative;
    height: 100%;

    :global {
      & .quill {
        flex: 1;
      }

      & strong {
        font-weight: bold !important;
      }

      & u {
        text-decoration: underline !important;
      }

      & em {
        font-style: italic !important;
      }

      & h2 {
        font-size: 16px;
        font-weight: bold;
        padding-top: variables(gutter);
        padding-bottom: variables(gutter);
      }

      & h3 {
        font-size: 6px;
        font-weight: bold;
        padding-top: variables(gutter);
        padding-bottom: variables(gutter);
      }

      & .ql-editor h3,
      .ql-editor h2,
      .ql-editor h1 {
        padding-bottom: variables(gutter) * 0.5;
      }

      & .ql-container {
        border: unset !important;
        font-size: 16px;
      }

      & .ql-editor p,
      .ql-editor li {
        padding-bottom: variables(gutter) * 0.25 !important;
      }

      & .ql-editor {
        padding: unset !important;
        flex-direction: column;
        display: flex;
        margin-top: variables(xLargeGutter) * 0.5;

        &::before {
          left: 9px !important;
          opacity: 0.6;
          color: colors(graphene);
          font-style: normal !important;
          font-weight: 500;
          min-width: 20px;
        }
      }

      & .ql-editor ol,
      .ql-editor ul {
        padding-left: 0 !important;
      }

      & .ql-editor li:not(.ql-direction-rtl)::before {
        text-align: center;
      }
    }
  }

  .optionsContainer {
    padding-top: variables(gutter) * 0.5;
  }
}
