import _debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactQuill from 'react-quill-new';
import useTranslation from 'shared/utils/hooks/useTranslation';
import removeHtmlTagsInstring from 'shared/utils/toolkit/removeHtmlTagsInstring';
import Flex from '../Flex';
import Icon from '../Icon';
import 'react-quill-new/dist/quill.snow.css';
import Typography from '../Typography';
import cnj from '../utils/cnj';
import useTheme from '../utils/useTheme';
import classes from './RichText.component.module.scss';
import type { ReactQuillEditorProps } from 'shared/types/richTextEditorProps';

const ReactQuillEditor = <RO extends boolean>({
  maxLength,
  visibleCharCounter,
  error,
  helperText,
  onChange,
  value,
  readOnly,
  classNames,
  styles,
  debounce = 1000,
  ...rest
}: ReactQuillEditorProps<RO>) => {
  const { isDark } = useTheme();
  const { t } = useTranslation();
  const quillRef = useRef<ReactQuill>(null);
  const [_value, _setValue] = useState(value ?? '');
  const [focused, setFocused] = useState(false);

  const debounceFn = useCallback(
    _debounce(onChange || (() => {}), debounce),
    []
  );
  const pureText = useMemo(
    () => removeHtmlTagsInstring(_value || ''),
    [_value]
  );

  useEffect(() => {
    if (!readOnly && quillRef.current) {
      const editor = quillRef.current.getEditor();
      editor?.enable();
    }
  }, [readOnly]);

  const handleChange = (value: string) => {
    _setValue(value);
    debounceFn(value);
  };

  function checkMaxLength(e: any) {
    if (!maxLength) return;
    const editor = quillRef.current?.getEditor();
    const innerText = editor?.getText() || '';
    const contentPureLength = removeHtmlTagsInstring(innerText)?.length;
    const isExceeding = contentPureLength > maxLength;
    if (isExceeding && e?.key !== 'Backspace') {
      e?.preventDefault();
    }
  }

  const handleContainerClick = (e: React.MouseEvent) => {
    if (!readOnly && quillRef.current) {
      const editor = quillRef.current.getEditor();
      const isToolbarClick = (e.target as HTMLElement).closest('#toolbar');
      if (!isToolbarClick) {
        editor?.focus();
      }
    }
  };

  return (
    <Flex className={cnj(classes.root, classNames?.wrapper)}>
      <Flex
        onClick={handleContainerClick}
        className={cnj(
          classes.editorRoot,
          isDark
            ? classes.quillEditorContainerDark
            : classes.quillEditorContainerLight,
          focused && classes.focused,
          rest.jobCreation && classes.jobCreationStyles,
          styles
        )}
      >
        {!readOnly && (
          <div id="toolbar" className={classes.toolbar}>
            <span className={cnj(classes.formats, 'ql-formats')}>
              <button
                type="button"
                className={cnj(classes.header, 'ql-header')}
              >
                <Icon size={14} name="heading" type="far" />
              </button>
            </span>
            <span className={cnj(classes.formats, 'ql-formats')}>
              <button aria-label="Bold" className="ql-bold" type="button" />{' '}
              <button aria-label="Italic" className="ql-italic" type="button" />
              <button
                aria-label="Underline"
                className="ql-underline"
                type="button"
              />
            </span>
            <span className={cnj(classes.formats, 'ql-formats')}>
              <button
                aria-label="Bullet_List"
                className="ql-list"
                value="bullet"
                type="button"
              />
              <button
                aria-label="List"
                className="ql-list"
                value="ordered"
                type="button"
              />
            </span>
          </div>
        )}
        <ReactQuill
          {...rest}
          ref={quillRef}
          readOnly={readOnly}
          onChange={handleChange}
          value={readOnly ? value : _value}
          modules={{
            toolbar: !readOnly && {
              container: '#toolbar',
            },
          }}
          formats={formats}
          className={cnj(classes.container, rest.className)}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          onKeyDown={checkMaxLength}
        />
      </Flex>
      {(error || helperText) && (
        <Flex className={classes.helperWrap}>
          <Typography
            size={13}
            height={15}
            color="border"
            className={cnj(classes.helperText, error && classes.errorText)}
          >
            {t(error as string) || helperText}
          </Typography>
        </Flex>
      )}
      {!error && visibleCharCounter && (
        <Flex className={classes.helperWrap}>
          <Typography
            size={13}
            height={15}
            color="border"
            className={classes.maxLength}
          >{`${pureText?.length || 0}/${maxLength}`}</Typography>
        </Flex>
      )}
    </Flex>
  );
};

export default ReactQuillEditor;

export const formats = [
  'bold',
  'italic',
  'underline',
  'list',
  'header',
  'link',
];
export const modules = {
  toolbar: [
    ['header'],
    ['bold', 'italic', 'underline'],
    [{ list: 'ordered' }, { list: 'bullet' }],
  ],
};
