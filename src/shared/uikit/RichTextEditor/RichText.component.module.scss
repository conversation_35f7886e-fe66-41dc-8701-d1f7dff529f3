@import '/src/shared/theme/theme.scss';

@layer uikit {
  .root {
    flex: 1;
  }
  .errorText {
    color: colors(error) !important;
  }
  .editorRoot {
    flex: 1;
    padding-inline: variables(gutter);
    padding-top: variables(xLarge<PERSON>utter) * 0.5;
    padding-bottom: variables(xLarge<PERSON>utter) * 0.5;
    border: 1px solid colors(techGray_20);
    border-radius: variables(gutter) * 0.25;
    transition: transitions(smooth);
    word-break: break-word;
    caret-color: colors(thirdText);
    gap: variables(xLarge<PERSON>utter) * 0.5;
    font-family: fonts(regular);
    pointer-events: auto;
    opacity: 1;
    user-select: auto;
    position: relative;
    &:hover {
      border-color: colors(secondaryDisabledText);
    }
    &.focused {
      border-color: colors(brand);
    }
    :global {
      & .ql-toolbar.ql-snow {
        border: 1px solid colors(techGray_10) !important;
        border-radius: 4px !important;
        .ql-formats {
          margin: 0;
          padding-inline: variables(gutter) * 0.25 !important;
          border-right: 1px solid colors(techGray_10) !important;
          &:first-child {
            padding-left: unset !important;
          }
          &:last-child {
            border-right: unset !important;
          }
        }
      }
      & .quill {
        flex: 1 !important;
        .ql-container.ql-snow {
          border: unset !important;
          .ql-editor {
            padding: 0 !important;
            font-size: variables(gutter) - 1 !important;
            & *::selection {
              color: colors(white) !important;
              background: colors(brand) !important;
            }
            strong {
              font-weight: 700 !important;
            }
            u {
              text-decoration: underline !important;
            }
            em {
              font-style: italic !important;
            }
            h1 {
              font-weight: 700 !important;
              padding-bottom: variables(gutter) * 0.5 !important;
            }
            p,
            li {
              padding-bottom: variables(gutter) * 0.25 !important;
            }
            ol,
            ul {
              padding-left: variables(gutter) * 0.25 !important;
              li {
                &::before {
                  display: none !important;
                }
                &[data-list='bullet'] {
                  .ql-ui::before {
                    content: "'\2022'";
                  }
                }
              }
            }
            &.ql-blank {
              &::before {
                left: 0 !important;
                color: colors(border) !important;
                font-style: normal !important;
                font-weight: 500 !important;
              }
            }
          }
        }
      }
    }
    &.quillEditorContainerDark {
      :global {
        & .ql-editor {
          color: colors(smoke) !important;
        }
        & .ql-formats button {
          path,
          line,
          rect {
            stroke: colors(disabledGray) !important;
          }
          &:hover,
          &.ql-active {
            path,
            line,
            rect {
              stroke: colors(brand) !important;
            }
          }
          &.ql-header:hover,
          &.ql-header.ql-active {
            path {
              fill: colors(brand) !important;
            }
          }
        }
      }
    }
    &.quillEditorContainerLight {
      :global {
        & .ql-editor {
          color: colors(smoke_coal) !important;
        }
        & .ql-formats button {
          path,
          line,
          rect {
            stroke: colors(graphene) !important;
          }
          &:hover,
          &.ql-active {
            path,
            line,
            rect {
              stroke: colors(brand) !important;
            }
          }
          &.ql-header path {
            fill: colors(graphene) !important;
          }
          &.ql-header:hover,
          &.ql-header.ql-active {
            path {
              fill: colors(brand) !important;
            }
          }
        }
      }
    }
    &.jobCreationStyles {
      :global {
        & .quill {
          .ql-container.ql-snow {
            p {
              margin-block-end: variables(gutter) !important;
              &:has(+ ol) {
                margin-block-end: variables(xLargeGutter) * 0.25 !important;
              }
            }
            strong {
              font-size: 17px !important;
            }
            ol {
              margin-block-end: variables(gutter) !important;
            }
            *:last-child {
              margin-block-end: 0 !important;
            }
          }
        }
      }
    }
  }

  .helperWrap {
    margin-top: variables(gutter) * 0.25;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
