@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .rootTimeline {
    flex-shrink: 0;
  }

  .showAll {
    flex-direction: row;
    align-self: flex-end;
    align-items: center;
    color: colors(primaryText);
    cursor: pointer;
    margin-top: variables(gutter);
  }
  .chevron {
    font-size: 10px;
    margin-left: 6px;
  }

  @media (min-width: breakpoints(tablet)) {
    .showAll {
      margin-top: variables(largeGutter);
    }
  }
}
