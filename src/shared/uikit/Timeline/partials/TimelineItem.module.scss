@import '/src/shared/theme/theme.scss';

$IMAGE_DIM: 40px;

.rootTimelineItem {
  position: relative;
  flex-direction: row;
  align-items: stretch;
  margin-top: variables(gutter);
}

.imageWrapper {
  justify-content: flex-start;
  margin-right: variables(gutter);
  width: $IMAGE_DIM;
  height: $IMAGE_DIM;
}

.image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.infoWrapper {
  justify-content: flex-start;
  width: calc(100% - #{$IMAGE_DIM + 20}px);
}

.stepsWrapper {
  margin-top: variables(gutter) * 0.5;
}

.multiSteps {
  margin-top: variables(gutter);
  flex-shrink: 0;
}

.editIcon {
  position: absolute;
  right: 0;
  top: 0;
}

@media (min-width: breakpoints(tablet)) {
  .rootTimelineItem {
    margin-top: variables(largeGutter);
  }
  .imageWrapper {
    margin-right: variables(largeGutter);
  }
  .stepsWrapper {
    margin-top: variables(largeGutter) * 0.5;
  }
  .multiSteps {
    margin-top: variables(largeGutter);
  }
}
