@import '/src/shared/theme/theme.scss';

.rootSteps {
  position: relative;
  margin-bottom: variables(gutter);
  &:last-child {
    margin-bottom: 0;
  }
}
.description {
  margin-top: variables(gutter);
}
.readMore {
  cursor: pointer;
  align-self: flex-start;
  margin-top: variables(gutter) * 0.5;
}
.editIcon {
  position: absolute;
  right: 0;
  top: 0;
}

@media (min-width: breakpoints(tablet)) {
  .rootSteps {
    margin-bottom: variables(largeGutter);
  }
  .description {
    margin-top: variables(largeGutter);
  }
}
