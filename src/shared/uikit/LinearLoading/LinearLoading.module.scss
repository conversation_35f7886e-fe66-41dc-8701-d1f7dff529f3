@import '/src/shared/theme/theme.scss';

@layer organism {
  @keyframes loader {
    0% {
      left: 0%;
      right: 0%;
      width: 0%;
    }
    50% {
      left: 0%;
      right: 0%;
      width: 100%;
    }
    99% {
      left: 100%;
      right: 0%;
      width: 0%;
    }
  }

  .linearLoading {
    background-color: colors(lightGray);
    overflow: hidden;
    height: 4px;
    width: 100%;
    display: flex;
    z-index: 1;

    &::after {
      content: '';
      background-color: colors(brand);
      width: 25%;
      height: 100%;
      position: relative;
      animation: loader 1.5s;
      animation-iteration-count: infinite;
      animation-timing-function: ease-in-out;
    }
  }
}
