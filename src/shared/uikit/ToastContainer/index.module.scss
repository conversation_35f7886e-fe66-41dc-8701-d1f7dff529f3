@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .unset {
    background: unset;
    box-shadow: unset;
    min-height: unset;
    padding: 0;
    margin-bottom: 0;
    border-radius: 12px;
    font-family: unset;
    width: unset;
  }
  .rootClassName {
    width: 100%;
    border-radius: 8px;
    padding: variables(gutter) variables(gutter) * 0.5;
  }
  .shadow {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    background: colors(background) !important;
    border-radius: 12px !important;
    width: 400px;
    padding: variables(largeGutter) !important;
  }
  .shadowDark {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  }

  .toastClassName {
  }
  .bodyClassName {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: colors(background);
    border-radius: 8px;
    padding: variables(gutter);
    position: relative;
  }

  @media (min-width: breakpoints(tablet)) {
    .bodyClassName {
      padding: variables(largeGutter);
      border-radius: 12px;
    }
    .rootClassName {
      width: unset;
      border-radius: 12px;
      padding: unset;
    }
    .shadow {
      border-radius: 12px;
    }
  }
}
