@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .jobWorkPlaceTypeComponentRoot {
    flex-direction: row;
    align-items: center;
  }
  .itemWrapper {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
  }
  .horizontalPadding {
    padding: 0 8px;
  }
  .item {
    position: relative;
    align-items: center;
    justify-content: center;
    border: 1px solid colors(gray_techGray20);
    border-radius: 4px;
    height: 100px;
    min-height: 100px;
    max-height: 100px;
    &:hover {
      border: 1px solid colors(brand);
      background-color: colors(brand_10);
    }
  }
  .selectedItem {
    border: 1px solid colors(brand);
    background-color: colors(brand);
    &:hover {
      background-color: colors(brand);
    }
  }
  .toolTip {
    position: absolute;
    top: 4px;
    right: 4px;
  }
}
