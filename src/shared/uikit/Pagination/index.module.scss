@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .paginationRoot {
    flex-direction: row;
    justify-content: center;
    user-select: none;
    width: 100%;
    position: relative;
  }
  .button {
    cursor: pointer;
    position: absolute;
    background-color: colors(transparent);
    &:hover {
      background-color: colors(hoverPrimary);
      border-radius: 4px;
    }
  }
  .leftButton {
    left: variables(gutter);
  }
  .rightButton {
    right: variables(gutter);
  }
  .page {
    cursor: pointer;
    width: 40px;
    height: 32px;
    align-items: center;
    justify-content: center;
    margin: 0;
    &:hover {
      background-color: colors(hoverPrimary);
      border-radius: 4px;
    }
  }
  .dots {
    pointer-events: none;
    padding-bottom: variables(gutter) * 0.5;
  }
  .current {
    color: colors(brand);
    pointer-events: none;
    background-color: colors(brand_10);
    border-radius: 4px;
  }
  .disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}
