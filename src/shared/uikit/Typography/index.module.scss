@import '/src/shared/theme/theme.scss';

@layer uikit {
  .base {
    font-style: normal;
    line-height: 22px;
    font-size: 15px;
    color: colors(primaryText);
    font-weight: normal;
    font-family: fonts(regular);
    -webkit-font-smooth: antialiased;
    display: flex;
    flex-direction: column;
    &::selection {
      color: colors(white);
      background: colors(brand);
    }
  }

  .modifiedWithNoWrap {
    white-space: nowrap;
  }

  .modifiedWithClick {
    cursor: pointer;
  }

  .truncate {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .isWordWrap {
    white-space: pre-wrap;
    word-break: break-word;
  }
  .lineNumber {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .color-inherit {
    color: inherit;
  }

  @each $color-name in map-keys($theme-colors) {
    .color-#{'' + $color-name} {
      color: colors($color-name);
    }
  }

  @each $font-name in map-keys($font-values) {
    .font-#{"" + $font-name} {
      font-family: fonts($font-name);
    }
  }
}
