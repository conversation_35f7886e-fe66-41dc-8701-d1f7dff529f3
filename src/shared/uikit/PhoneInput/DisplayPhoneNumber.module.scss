@import '/src/shared/theme/theme.scss';

@layer uikit {
  .containerClass {
    border: unset;
    border-radius: unset;
    background: unset;
    height: unset;
    width: 100%;
  }

  .inputClass {
    font-size: 15px !important;
    color: colors(thirdText) !important;
    background: colors(transparent) !important;
    border-radius: 3px !important;
    border: unset !important;
    line-height: 21px !important;
    height: unset !important;
    padding: unset !important;
    width: 20 * 12 !important;
    //width: [length * 12, '!important'],

    &:disabled {
      cursor: default !important;
    }

    &::selection {
      color: colors(white);
      background: colors(brand);
    }

    &::placeholder {
      color: colors(colorIconForth2);
      font-size: 14px;
      font-weight: 400;
    }
  }

  .buttonClass {
    display: none;
  }

  .dropdownClass {
    display: none;
  }

  .searchClass {
    display: none;
  }

  @media (pointer: coarse) {
    .inputClass {
      &:hover {
        background-color: colors(transparent) !important;
      }

      &:focus {
        background-color: colors(transparent) !important;
      }

      &:active {
        background-color: colors(transparent) !important;
      }

      &:visited {
        background-color: colors(transparent) !important;
      }
    }
  }
}
