@import '/src/shared/theme/theme.scss';

@layer uikit {
  .checkBoxRoot {
    flex-direction: row;
    align-items: center;
    transition: transitions(smooth);
  }
  .icon {
    align-self: flex-start;
    &:hover {
      background-color: colors(transparent);
      & > span {
        color: colors(primaryText);
      }
    }
    & > span {
      font-size: 20px;
      color: colors(primaryDisabledText);
    }
  }

  .selectedIcon {
    &:hover {
      & > span {
        color: colors(trench);
      }
    }

    & > span {
      font-size: 20px;
      color: colors(brand);
    }
  }

  .textContainer {
    margin-left: 12px;
  }
  .marginTop {
    margin-top: -3px;
  }
  .errorBoundary {
    color: colors(error);
  }

  @media (min-width: breakpoints(tablet)) {
    .checkBoxRoot {
      &:hover {
        background-color: colors(hoverPrimary);
      }
    }
  }
}
