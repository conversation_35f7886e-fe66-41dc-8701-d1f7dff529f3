@import '/src/shared/theme/theme.scss';

.text {
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.icon {
  margin-left: 5px;
}

.moreButton {
  display: none;
}

.moreButtonActive {
}

@media (min-width: breakpoints(tablet)) {
  .moreButton {
    display: flex;
    cursor: pointer;
    padding: variables(gutter) 0 0 0;
    text-align: center;
    font-style: normal;
    font-size: 15px;
    line-height: 20px;
    font-weight: normal;
    font-family: fonts(regular);
    margin-left: variables(gutter);

    & * {
      color: colors(thirdText);
    }

    &:hover {
      background: colors(hoverPrimary);

      & * {
        color: colors(thirdText);
      }
    }

    &:focus,
    &:hover,
    &:visited,
    &:link,
    &:active {
      text-decoration: none;
      outline: none;
    }

    &:after {
      height: 4px;
      display: block;
      content: '';
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
      margin-top: variables(gutter) * 0.5;
      width: 82px;
    }
  }

  .moreButtonActive {
    font-family: fonts(bold);

    & * {
      color: colors(brand);
    }

    &:hover {
      color: colors(brand);

      & * {
        color: colors(brand);
      }
    }

    &:after {
      background: colors(brand);
    }
  }
}
