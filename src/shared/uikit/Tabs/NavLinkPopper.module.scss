@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .navLinkPopperRoot {
    border-radius: 4px;
    padding: 0 variables(gutter) * 0.5;
    background-color: colors(background);
    min-width: 114px;
    height: 40px;
    justify-content: center;
    &:hover {
      background-color: colors(hoverPrimary);
    }
  }
  .activeRoute {
    cursor: default;
    background: colors(brand20_brand);
    & * {
      color: colors(brand_white);
    }
    &:hover {
      background-color: colors(brand20_brand);
    }
  }
}
