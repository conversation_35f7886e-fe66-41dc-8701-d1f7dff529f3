@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .tabsRoot {
    background: colors(background2);
    flex-shrink: 0;
  }
  .innerWrapper {
    width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -moz-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    flex-shrink: 0;
  }
  .linksRoot {
    background: colors(background);
    width: 100%;
  }
  .linkAndActionWrapper {
    flex-direction: row;
    position: relative;
    overflow: auto;
    white-space: nowrap;
    scrollbar-width: none;
    gap: variables(gutter) * 0.5;
  }
  .buttonsWrapper {
    flex-direction: row;
    align-items: center;
    flex: 1;
    overflow: auto;
    flex-shrink: 0;
    padding: 0 variables(gutter);
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .badgeButtonsWrapper {
    padding: variables(gutter) * 0.5 variables(gutter);
  }
  .divider {
    height: 24px;
    margin: 0 0 0 variables(gutter) * 0.5;
  }
  .tabsInMobileView {
  }
  .content {
  }

  .skeleton {
    height: 32px;
    width: 125px;
    border-radius: 100px;
    margin-top: variables(gutter) * 0.5;
    margin-bottom: variables(gutter) * 0.5;
  }

  @media (min-width: breakpoints(tablet)) {
    .skeleton {
      margin-top: variables(gutter);
      margin-bottom: variables(gutter);
    }
    .linksRoot {
      align-items: center;
    }
    .tabsInMobileView {
      display: none;
    }
    .linkAndActionWrapper {
      width: 100%;
      max-width: variables(contentMaxWidth);
      padding: 0 variables(largeGutter);
      align-items: center;
    }
    .buttonsWrapper {
      padding: 0;
    }
    .badgeButtonsWrapper {
      padding: variables(gutter) 0;
    }
    .content {
      max-width: variables(contentMaxWidth);
      width: 100%;
      align-self: center;
      flex-wrap: wrap;
      padding: 0 variables(largeGutter) 0;
    }
  }
}
