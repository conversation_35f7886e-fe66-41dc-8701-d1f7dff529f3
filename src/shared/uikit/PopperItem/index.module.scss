@import '/src/shared/theme/theme.scss';

@layer uikit3 {
  .popperItemRoot {
    justify-content: flex-start;
    border-radius: 0;
    &:hover {
      border-radius: 0;
    }
  }
  .active {
    background: colors(brand);
    pointer-events: none;
  }
  .labelClassName {
    margin-left: 0;
    line-height: 24px;
  }
  .labelClassNameHasIcon {
    margin-left: variables(gutter) * 0.5;
  }

  .labelsContainer {
    width: 180px;
  }

  @media (min-width: breakpoints(tablet)) {
    .labelsContainer {
      width: auto;
    }
    .popperItemRoot {
      align-items: center;
      justify-content: flex-start;
      border-radius: 4px;
      &:hover {
        border-radius: 4px;
      }
    }
  }
}
