import React from 'react';
import PortalAndRoleBadge from '@shared/components/molecules/PortalAndRoleBadge';
import Avatar from '@shared/uikit/Avatar';
import BaseButton from '@shared/uikit/Button/BaseButton';
import Flex from '@shared/uikit/Flex';
import RadioButton from '@shared/uikit/RadioButton';
import OverflowTip from '@shared/uikit/Typography/OverflowTip';
import cnj from '@shared/uikit/utils/cnj';
import classes from './index.module.scss';

export interface SwitchNotificationItemProps {
  image?: string;
  title?: string;
  subTitle?: string;
  isPage?: boolean;
  isActive?: boolean;
  role?: string;
  onClick?: VoidFunction;
  variant?: 'portalItem' | 'normal';
  visibleRightAction?: boolean;
  className?: string;
  unseenMessagesCount?: string;
  RightAction?: JSX.Element;
  id?: string;
}

export default function SwitchNotificationItem({
  image,
  title,
  subTitle,
  isPage = false,
  isActive,
  role,
  onClick,
  variant = 'normal',
  unseenMessagesCount,
  visibleRightAction = true,
  className,
  RightAction,
  id,
}: SwitchNotificationItemProps) {
  return (
    <BaseButton onClick={onClick} key={`list-item-${id}`}>
      <Flex className={cnj(classes.pageGridCardRoot)}>
        <Avatar
          isCompany={isPage}
          size="smd"
          imgSrc={image}
          bordered
          className={classes.avatarContainer}
          badgeNumber={unseenMessagesCount}
          name={title}
        />

        <Flex className={classes.contentContainer}>
          <Flex className={classes.titleGroup}>
            <OverflowTip color="smoke_coal" font="700" size={16} height={19}>
              {title}
            </OverflowTip>
            {role && <PortalAndRoleBadge role={role} />}
          </Flex>
          {variant !== 'portalItem' && (
            <OverflowTip
              mt={2}
              color="secondaryDisabledText"
              size={14}
              height={16}
            >
              {subTitle}
            </OverflowTip>
          )}
        </Flex>
        {RightAction ||
          (visibleRightAction ? (
            <RadioButton
              styles={{ root: classes.radioButton }}
              value={isActive}
            />
          ) : null)}
      </Flex>
    </BaseButton>
  );
}
