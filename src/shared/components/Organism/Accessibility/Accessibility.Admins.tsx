import React, { useState, useRef } from 'react';
import PortalAndRoleBadge from 'shared/components/molecules/PortalAndRoleBadge';
import ConfirmPasswordModal from 'shared/components/Organism/ConfirmPasswordModal';
import AvatarCard from 'shared/uikit/AvatarCard';
import IconButton from 'shared/uikit/Button/IconButton';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import Flex from 'shared/uikit/Flex';
import Form from 'shared/uikit/Form';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import fuseSearch from 'shared/uikit/utils/fuseSearch';
import { deletePageMember, givePageAccess } from 'shared/utils/api/page';
import formValidator from 'shared/utils/form/formValidator';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './Accessibility.component.module.scss';
import type { UserType } from 'shared/types/user';

interface Props {
  casAssignRole: boolean;
  admins: Array<UserType>;
  members: Array<UserType>;
  refetch: VoidFunction;
  pageId: string;
}

const AccessibilityAdmins: React.FC<Props> = ({
  casAssignRole,
  admins,
  members,
  refetch,
  pageId,
}) => {
  const { t } = useTranslation();
  const formDataRef = useRef<any>(null);
  const isDeleteRef = useRef<any>(false);
  const formRef = useRef<any>(null);
  const { openConfirmDialog } = useOpenConfirm();
  const [visibleConfirmPassModal, setVisibleConfirmPassModal] = useState(false);
  const [filteredOption, setFilteredOption] = useState<any>(members);
  const search = fuseSearch(members, {
    keys: ['user.fullName', 'user.username'],
  });

  const onChangeInput = (input: string) => {
    const items = search(input).slice(0, 10);
    setFilteredOption(items);
  };

  const closeConfirmPasswordModal = () => {
    setVisibleConfirmPassModal(false);
    formRef.current?.setSubmitting(false);
  };

  const { mutate: deletePageAccess } = useReactMutation({
    apiFunc: deletePageMember,
  });

  const { mutate: putPageAccess } = useReactMutation({
    apiFunc: givePageAccess,
  });

  const onSuccessHandler = () => {
    formRef.current?.resetForm();
    refetch();
    isDeleteRef.current = false;
    closeConfirmPasswordModal();
  };

  const deleteRoleHandler = (password?: string) => {
    deletePageAccess(
      {
        pageId,
        credentials: password ? { password } : undefined,
        ...formDataRef.current,
      },
      {
        onSuccess: onSuccessHandler,
      }
    );
  };

  const postNewRoleHandler = (password?: string) => {
    putPageAccess(
      {
        pageId,
        credentials: password ? { password } : undefined,
        ...formDataRef.current,
      },
      {
        onSuccess: onSuccessHandler,
      }
    );
  };

  const openConfirm = ({ userId }: any) =>
    openConfirmDialog({
      title: t('remove_accessibility'),
      message: t('r_y_s_w_r_role'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('cancel'),
      confirmCallback: () => {
        isDeleteRef.current = true;
        formDataRef.current = {
          userId,
          pageId,
          role: 'ADMIN',
        };
        setVisibleConfirmPassModal(true);
      },
    });

  return (
    <Flex>
      <Typography font="700" color="smoke_coal" size={16} height={20}>
        {t('admins')}
      </Typography>

      {casAssignRole && (
        <Form
          initialValues={{
            user: null,
          }}
          onSuccess={({ user }, ref) => {
            formRef.current = ref;
            isDeleteRef.current = false;
            formDataRef.current = {
              userId: user.value,
              pageId,
              newRole: 'ADMIN',
            };
            setVisibleConfirmPassModal(true);
          }}
          local
          className={classes.adminFormWrapper}
          validationSchema={formValidator.object().shape({
            user: formValidator
              .object()
              .test('user', 'select_one_of_sug_users', (val) => val?.value),
          })}
        >
          {() => (
            <>
              <DynamicFormBuilder
                groups={[
                  {
                    name: 'user',
                    label: t('admin'),
                    cp: 'avatarAsyncAutoComplete',
                    options: filteredOption.map(({ user }) => ({
                      value: user.id,
                      label: user.fullName,
                      image: user.croppedImageUrl,
                      helperText: user.username,
                    })),
                    visibleRightIcon: true,
                    visibleOptionalLabel: false,
                    onChangeInput,
                  },
                ]}
              />
              <SubmitButton label={t('add')} />
            </>
          )}
        </Form>
      )}

      {admins?.length > 0 && (
        <Flex className={cnj('responsive-margin-top', classes.listWrapper)}>
          {admins.map(({ user }) => {
            const userData = {
              image: user.croppedImageUrl,
              title: user.fullName,
              subTitle: user.usernameAtSign,
              role: () => <PortalAndRoleBadge role="ADMIN" />,
            };

            return (
              <Flex>
                <AvatarCard
                  key={user.id}
                  containerProps={{ className: classes.avatarCard }}
                  noHover
                  data={userData}
                  avatarProps={{ name: user?.fullName }}
                  action={
                    casAssignRole ? (
                      <IconButton
                        onClick={() => openConfirm({ userId: user.id })}
                        className={classes.actionBtn}
                        type="far"
                        size="md18"
                        name="remove-follower"
                      />
                    ) : undefined
                  }
                />
              </Flex>
            );
          })}
        </Flex>
      )}
      <ConfirmPasswordModal
        isOpen={visibleConfirmPassModal}
        onClose={closeConfirmPasswordModal}
        onBack={closeConfirmPasswordModal}
        formProps={{
          local: true,
          onSuccess: ({ password }: any) =>
            isDeleteRef.current
              ? deleteRoleHandler(password)
              : postNewRoleHandler(password),
        }}
      />
    </Flex>
  );
};

export default AccessibilityAdmins;
