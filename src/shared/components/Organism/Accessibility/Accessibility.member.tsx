import React, { useState, Fragment } from 'react';
import Button from '@shared/uikit/Button';
import Icon from '@shared/uikit/Icon';
import PortalAndRoleBadge from 'shared/components/molecules/PortalAndRoleBadge';
import AssignAccessibility from 'shared/components/Organism/Accessibility/Accessibility.AssignAccessibility';
import AvatarCard from 'shared/uikit/AvatarCard';
import IconButton from 'shared/uikit/Button/IconButton';
import Carousel from 'shared/uikit/Carousel';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import DividerVertical from 'shared/uikit/Divider/DividerVertical';
import Flex from 'shared/uikit/Flex';
import Tooltip from 'shared/uikit/Tooltip';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import { deletePortalMember } from 'shared/utils/api/page';
import { PAGE_MEMBER_STATUS } from 'shared/utils/constants/enums';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import classes from './Accessibility.component.module.scss';
import type { UserType } from 'shared/types/user';

const MAX = 3;

interface Props {
  casAssignRole: boolean;
  members: Array<UserType>;
  refetch: VoidFunction;
  pageId: string;
}

const AccessibilityMember: React.FC<Props> = ({
  casAssignRole,
  members,
  refetch,
  pageId,
}) => {
  const { t } = useTranslation();
  const [selected, setSelected] = useState<number>(null);
  const { openConfirmDialog } = useOpenConfirm();

  const { mutate: deletePortalAccess } = useReactMutation({
    apiFunc: deletePortalMember,
  });

  const onSuccessHandler = () => {
    refetch();
  };

  const onDeleteHandler = ({ userId, role, portal }: any) => {
    deletePortalAccess(
      {
        pageId,
        userId,
        role,
        portal,
      },
      {
        onSuccess: onSuccessHandler,
      }
    );
  };

  const openConfirm = (formData) =>
    openConfirmDialog({
      title: t('remove_accessibility'),
      message: t('r_y_s_w_r_role'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('cancel'),
      confirmCallback: () => {
        onDeleteHandler(formData);
      },
    });

  return (
    <Flex>
      <Typography font="700" color="smoke_coal" size={16} height={20}>
        {t('members')}
      </Typography>
      {casAssignRole && (
        <AssignAccessibility pageId={pageId} refetch={refetch} />
      )}
      {members?.length > 0 && (
        <Flex className={cnj('responsive-margin-top', classes.listWrapper)}>
          {members.map(({ user, status, portalAccesses, userId }) => {
            const isSelected = selected === userId;
            const userData = {
              image: user.croppedImageUrl,
              title: user.fullName,
              subTitle: user.usernameAtSign,
              role: () =>
                Status({
                  portalAccesses,
                }),
            };
            const pendingPurchased = portalAccesses?.filter(
              (access) => access.status === 'PENDING_PURCHASE'
            );

            return (
              <Flex className="!relative" key={userId}>
                <AvatarCard
                  key={user.id}
                  containerProps={{ className: classes.avatarCard }}
                  noHover
                  data={userData}
                  avatarProps={{ name: user?.fullName }}
                  action={
                    casAssignRole ? (
                      <Flex className={classes.actionBtn}>
                        {pendingPurchased?.length > 0 && (
                          <Button
                            label={translateReplacer(
                              t('num_PENDING_PURCHASE'),
                              [pendingPurchased.length]
                            )}
                            variant="thin"
                            labelProps={{ size: 12 }}
                            schema="orange-semi-transparent"
                            className="!p-8 !ml-auto"
                            rightSvg={
                              <Tooltip
                                trigger={
                                  <Icon
                                    className="ml-8"
                                    color="pendingOrange"
                                    type="fal"
                                    name="info-circle"
                                    size={15}
                                  />
                                }
                              >
                                <Flex className={classes.roleWrapper}>
                                  {pendingPurchased.map((item) => (
                                    <PortalAndRoleBadge
                                      key={item.role}
                                      role={item.role}
                                    />
                                  ))}
                                </Flex>
                              </Tooltip>
                            }
                          />
                        )}
                        <IconButton
                          onClick={() =>
                            isSelected ? setSelected(null) : setSelected(userId)
                          }
                          type="far"
                          size="md18"
                          name={isSelected ? 'times' : 'pen'}
                        />
                      </Flex>
                    ) : undefined
                  }
                  avatarExtraCP={
                    status !== 'ACCEPTED' ? (
                      <IconButton
                        className={cnj(
                          '!absolute -bottom-[3px] left-[28px] !bg-error border border-solid !border-background rounded-xl',
                          status === 'PENDING' && '!bg-pendingOrange'
                        )}
                        iconProps={{ color: 'white' }}
                        name={status === 'PENDING' ? 'clock' : 'times'}
                        type="far"
                        size="sm"
                      />
                    ) : undefined
                  }
                />

                {!!isSelected && (
                  <Carousel
                    className={classes.roleBadgesWrapper}
                    visibleHead={false}
                    moveWalkDistance={208}
                  >
                    {portalAccesses.map((item) => (
                      <Fragment key={item.id}>
                        <Flex className={classes.roleBadge} key={item}>
                          <Tooltip
                            placement="top"
                            trigger={<PortalAndRoleBadge role={item.role} />}
                          >
                            <Typography size={13} color="tooltipText">
                              {`${t(item.portal)} portal`}
                            </Typography>
                          </Tooltip>
                          <Tooltip
                            placement="top"
                            trigger={
                              <IconButton
                                onClick={() =>
                                  openConfirm({
                                    pageId,
                                    userId,
                                    role: item.role,
                                    portal: item.portal,
                                  })
                                }
                                size="sm20"
                                type="far"
                                name="trash"
                              />
                            }
                          >
                            <Typography size={13} color="tooltipText">
                              {t('revoke_access')}
                            </Typography>
                          </Tooltip>
                        </Flex>
                        <DividerVertical distance={12} />
                      </Fragment>
                    ))}
                  </Carousel>
                )}
              </Flex>
            );
          })}
        </Flex>
      )}
    </Flex>
  );
};

export default AccessibilityMember;

const Status = ({ portalAccesses: items = [] }) => {
  const { t } = useTranslation();
  const portalAccesses = items?.filter(
    (access) => access.status !== 'PENDING_PURCHASE'
  );

  const roles = portalAccesses?.slice(0, MAX);
  const otherRoles =
    (portalAccesses?.length || 0) > MAX ? portalAccesses?.slice(MAX) : [];
  const count = (portalAccesses?.length || 0) - MAX;

  return (
    portalAccesses.length > 0 && (
      <Flex className={classes.roleWrapper}>
        {roles.map((item) => (
          <PortalAndRoleBadge key={item.role} role={item.role} />
        ))}
        {portalAccesses.length > MAX && (
          <Tooltip
            placement="top"
            trigger={
              <Typography>
                {translateReplacer(t('and_num_more'), [count])}
              </Typography>
            }
          >
            <Flex className={classes.roleWrapper}>
              {otherRoles.map((item) => (
                <PortalAndRoleBadge key={item.role} role={item.role} />
              ))}
            </Flex>
          </Tooltip>
        )}
      </Flex>
    )
  );
};
