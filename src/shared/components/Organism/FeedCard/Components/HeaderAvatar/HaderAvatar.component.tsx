import React from 'react';
import Avatar from 'shared/uikit/Avatar';
import ObjectLink from 'shared/uikit/Link/ObjectLink';
import classes from './HaderAvatar.component.module.scss';
import { AvatarProps } from 'shared/uikit/Avatar';

export interface Props {
  src: string;
  objectId: string;
  isBusiness?: boolean;
  size?: 'sm' | 'smd';
  username: string;
  avatarProps?: AvatarProps;
}

const HeaderAvatar = ({
  size = 'sm',
  src,
  objectId,
  isBusiness,
  username,
  avatarProps,
}: Props): JSX.Element => (
  <ObjectLink username={username} objectId={objectId}>
    <Avatar
      size={size}
      isCompany={isBusiness}
      imgSrc={src}
      className={classes.avatar}
      {...avatarProps}
    />
  </ObjectLink>
);

export default HeaderAvatar;
