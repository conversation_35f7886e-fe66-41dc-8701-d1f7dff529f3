import React, { useRef } from 'react';
import { useAuthState } from '@shared/contexts/Auth/auth.provider';
import { EntitiyPopper } from 'shared/components/molecules/EntityPopper/EntitiyPopper';
import FollowingButton from 'shared/components/molecules/FollowingButton/FollowingButton';
import {
  DefaultTagLinkComponent,
  DefaultTagTextComponent,
} from 'shared/components/molecules/TranslateReplacer';
import PostMediaBadge from 'shared/components/Organism/PostMediaBadge';
import useObjectNetwork from 'shared/hooks/api-hook/useObjectNetwork';
import Button from 'shared/uikit/Button';
import DateView from 'shared/uikit/DateView';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import useTheme from 'shared/uikit/utils/useTheme';
import useTranslation from 'shared/utils/hooks/useTranslation';
import HeaderAvatar from '../../Components/HeaderAvatar';
import PostHeaderText from '../../Components/PostHeaderText';
import Menu from '../../Components/PostMenuItems';
import useFeedElement from '../../Context/useFeedElement';
import classes from './header.component.module.scss';
import type { TextProps } from '@shared/uikit/Typography';

interface Props {
  className?: string;
  isSelected?: boolean;
  primaryTextProps?: Partial<TextProps<any>>;
  ref?: React.Ref<HTMLImageElement>;
}

const PostFeedLayoutHeader = ({
  className,
  isSelected,
  primaryTextProps,
  ref,
}: Props): JSX.Element => {
  const { isDark } = useTheme();
  const { t } = useTranslation();
  const { followHandler, isLoading } = useObjectNetwork();
  const headerTextContainerRef = useRef();
  const isLoggedIn = useAuthState('isLoggedIn');

  const {
    visibleNetworkInfoOnHeader,
    getPostOwnerInfo,
    isBusinessPost,
    post,
    follow,
    isMyPost,
    followPostOwner,
    undoFollowPostOwner,
    variant,
    myFollowingReactionOnPost,
    isSharedPost,
    feedMediaType,
    sharedEntityTitle,
  } = useFeedElement();

  const objectId = getPostOwnerInfo({
    userKey: 'userId',
    pageKey: 'id',
  });
  const avatar = getPostOwnerInfo({
    userKey: 'croppedImageUrl',
    pageKey: 'croppedImageUrl',
  });
  const username = getPostOwnerInfo({
    userKey: 'username',
    pageKey: 'username',
  });

  const postOwnerTitle = getPostOwnerInfo({
    userKey: 'fullName',
    pageKey: 'title',
  });
  const id = React.useMemo(
    () =>
      getPostOwnerInfo({
        userKey: 'userId',
        pageKey: 'id',
      }),
    []
  );

  const handleFollow = () => {
    followHandler(
      { id, isPage: isBusinessPost },
      {
        onSuccess: () => {
          followPostOwner();
        },
      }
    );
  };
  const handleUnFollowSuccess = () => {
    undoFollowPostOwner();
  };

  const isMultiLine = headerTextContainerRef?.current?.clientHeight > 30;

  return (
    <Flex
      flexDir="row"
      className={cnj(
        classes.feedHeaderContainer,
        isMultiLine && classes.multiLine,
        variant === 'search-view' && classes.feedHeaderSearchView,
        className
      )}
      ref={ref}
    >
      <EntitiyPopper username={objectId} useId isPage={isBusinessPost}>
        <HeaderAvatar
          objectId={objectId}
          username={username}
          isBusiness={isBusinessPost}
          src={avatar}
          size="smd"
          avatarProps={{ name: postOwnerTitle }}
        />
      </EntitiyPopper>
      <Flex className={classes.textContainer}>
        <Flex
          ref={headerTextContainerRef}
          flexDir="row"
          className={classes.textContent}
        >
          {variant === 'message' ? (
            <DefaultTagLinkComponent
              text={postOwnerTitle}
              objectId={objectId}
              username={username}
              textProps={{
                className: classes.truncate,
                ...primaryTextProps,
              }}
            />
          ) : variant === 'search-list-card' ? (
            <DefaultTagTextComponent
              text={postOwnerTitle}
              textProps={{
                className: classes.truncate,
                ...primaryTextProps,
              }}
            />
          ) : (
            <PostHeaderText
              height={18}
              objectId={objectId}
              username={username}
              postOwnerTitle={postOwnerTitle}
              tags={post?.tags}
              isSharedEntity={isSharedPost}
              locationTitle={post?.location?.title}
              sharedEntityTitle={
                sharedEntityTitle ? t(sharedEntityTitle) : undefined
              }
              textProps={primaryTextProps}
            />
          )}
        </Flex>

        <DateView
          value={post?.createdDate}
          font="400"
          height={14}
          size={12}
          mt={4}
          color="secondaryDisabledText"
        />
      </Flex>
      {variant === 'search-view' || variant === 'search-list-card' ? (
        <PostMediaBadge
          className={classes.postMediaBadge}
          feedMediaType={feedMediaType}
          isSharedPost={isSharedPost}
          isSelected={isSelected}
        />
      ) : variant !== 'message' ? (
        variant === 'light-box' ||
        variant === 'attachment-view' ||
        (visibleNetworkInfoOnHeader && myFollowingReactionOnPost && follow) ? (
          post.localFollow ? (
            <FollowingButton
              className={classes.followBtn}
              object={{
                isPage: isBusinessPost,
                id,
                username: null,
                name: null,
                needsAlert: false,
              }}
              onSuccess={handleUnFollowSuccess}
            />
          ) : null
        ) : visibleNetworkInfoOnHeader &&
          myFollowingReactionOnPost &&
          !follow &&
          !isMyPost ? (
          <Button
            className={classes.followBtn}
            isLoading={isLoading}
            schema={isDark ? 'secondary-dark' : 'semi-transparent'}
            leftIcon="plus"
            leftType="fas"
            label={t('follow_cap')}
            onClick={handleFollow}
          />
        ) : isLoggedIn ? (
          <Menu />
        ) : null
      ) : null}
    </Flex>
  );
};

export default PostFeedLayoutHeader;
