import React from 'react';
import { AutomationIcon } from '@shared/components/atoms/AutomationIcon';
import AvatarCard from '@shared/uikit/AvatarCard';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import classes from './index.module.scss';
import type { AvatarCardProps } from '@shared/uikit/AvatarCard';
import type { FC } from 'react';

interface UserInfoProps {
  image?: string;
  title: string | React.ReactNode;
  id: string;
  username: string;
  description: string;
  layoutTitle?: string;
  classNames?: {
    root?: string;
    layoutTitle?: string;
    container?: string;
    titleBox?: string;
    title?: string;
    description?: string;
    extraTitle?: string;
  };
  extraTitle?: string;
  automated?: boolean;
  action?: React.ReactNode;
  onClickAvatar?: (username: string) => void;
  avatarCardProps?: AvatarCardProps;
}

const UserInfo: FC<UserInfoProps> = (props) => {
  const {
    description,
    title,
    image,
    id,
    username,
    layoutTitle,
    classNames,
    extraTitle,
    automated,
    action,
    onClickAvatar,
    avatarCardProps,
  } = props;

  return (
    <Flex className={cnj(classes.root, classNames?.root)}>
      {!!layoutTitle && (
        <Typography
          className={cnj(classes.layoutTitle, classNames?.layoutTitle)}
          color="colorIconForth2"
        >
          {layoutTitle}
        </Typography>
      )}
      <AvatarCard
        avatarExtraCP={automated && <AutomationIcon />}
        data={{
          id,
          username,
          image,
          title: extraTitle ? (
            <TitleWithMoreText title={title} extraTitle={extraTitle} />
          ) : (
            title
          ),
          subTitle: description,
        }}
        withPadding={false}
        noHover
        subTitleProps={{
          color: 'secondaryDisabledText',
        }}
        action={action}
        containerProps={{ className: '!cursor-pointer' }}
        avatarProps={{
          onClick: (username?: string) => onClickAvatar?.(username),
          name: typeof title === 'string' ? title : undefined,
        }}
        {...avatarCardProps}
      />
    </Flex>
  );
};

export default UserInfo;

const TitleWithMoreText = ({
  title,
  extraTitle,
}: {
  title: string | React.ReactNode;
  extraTitle: string;
}) => (
  <Flex className={classes.titleWrap}>
    {typeof title === 'string' ? (
      <Typography className={cnj(classes.title)} font="700" color="smoke_coal">
        {title}
      </Typography>
    ) : (
      title
    )}
    {extraTitle && (
      <>
        <DividerVertical distance={8} />
        <Typography
          className={cnj(classes.extraTitle)}
          font="400"
          color="secondaryDisabledText"
        >
          {extraTitle}
        </Typography>
      </>
    )}
  </Flex>
);
