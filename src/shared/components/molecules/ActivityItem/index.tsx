import { type FC } from 'react';
import { AvatarCardProps } from '@shared/uikit/AvatarCard';
import { Time } from '@shared/utils/Time';
import formatDate from '@shared/utils/toolkit/formatDate';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import UserInfo from '../UserInfo';
import classes from './index.module.scss';
import ActivityBottomBox from './partials/ActivityBottomBox';
import ActivityTitleBox from './partials/ActivityTitleBox';
import type { ActivityProps } from 'shared/types/activityProps';
import useHistory from '@shared/utils/hooks/useHistory';
import useMakeYouRoute from '@shared/utils/hooks/useMakeYouRoute';

interface ActivityItemProps {
  item: ActivityProps;
  classNames?: {
    root?: string;
    content?: string;
  };
  avatarCardProps?: AvatarCardProps;
}

const ActivityItem: FC<ActivityItemProps> = ({
  item,
  classNames,
  avatarCardProps,
}) => {
  const history = useHistory();
  const makeYouRoute = useMakeYouRoute();
  const date = Time.convertBackFormatToFront(item?.createdDate);
  const dateAndTime = `${formatDate(date)} at ${
    Time.getFormTime(date, 0, true)?.label
  }`;

  const redirectToTeamMember = () => {
    history.push(makeYouRoute(item.user?.username));
  };

  return (
    <Flex className={cnj(classes.root, classNames?.root)}>
      <UserInfo
        title={<ActivityTitleBox item={item} />}
        automated={item?.automated}
        description={dateAndTime}
        image={item?.user?.croppedImageUrl}
        onClickAvatar={redirectToTeamMember}
        avatarCardProps={{
          ...avatarCardProps,
          avatarProps: {
            ...avatarCardProps?.avatarProps,
            name: item.user?.fullName,
          },
        }}
      />
      <Flex className={cnj(classes.content, classNames?.content)}>
        <ActivityBottomBox item={item} />
      </Flex>
    </Flex>
  );
};

export default ActivityItem;
